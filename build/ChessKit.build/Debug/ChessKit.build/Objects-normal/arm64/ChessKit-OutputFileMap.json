{"": {"diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/ChessKit-master.dia", "emit-module-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/ChessKit-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/ChessKit-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/ChessKit-master.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Attacks.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Attacks.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Attacks.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Attacks.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Attacks.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Attacks.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Attacks.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Attacks~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Bitboard.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Bitboard.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Bitboard.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Bitboard.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Bitboard.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Bitboard.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Bitboard.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Bitboard~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PieceSet.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PieceSet.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PieceSet.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PieceSet.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PieceSet.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PieceSet.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PieceSet.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PieceSet~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square+BB.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square+BB.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square+BB.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square+BB.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square+BB.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square+BB.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square+BB.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square+BB~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Board.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Board.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Board.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Board.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Board.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Board.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Board.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Board~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Clock.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Clock.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Clock.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Clock.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Clock.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Clock.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Clock.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Clock~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Configuration.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Configuration.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Configuration.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Configuration.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Configuration.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Configuration.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Configuration.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Configuration~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Game.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Game.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Game.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Game.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Game.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Game.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Game.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Game~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Move.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Move.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Move.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Move.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Move.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Move.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Move.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Move~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/MoveTree.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/MoveTree.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/MoveTree.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/MoveTree.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/MoveTree.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/MoveTree.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/MoveTree.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/MoveTree~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser+Regex.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser+Regex.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser+Regex.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser+Regex.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser+Regex.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser+Regex.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser+Regex.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser+Regex~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENParser.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENParser.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENParser.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENParser.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENParser.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENParser.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENParser.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENParser~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENValidator.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENValidator.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENValidator.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENValidator.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENValidator.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENValidator.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENValidator.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENValidator.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENValidator~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser+Regex.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser+Regex.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser+Regex.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser+Regex.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser+Regex.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser+Regex.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser+Regex.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser+Regex~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser+Regex.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser+Regex.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser+Regex.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser+Regex.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser+Regex.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser+Regex.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser+Regex.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser+Regex~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Piece.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Piece.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Piece.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Piece.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Piece.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Piece.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Piece.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Piece~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Position.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Position.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Position.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Position.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Position.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Position.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Position.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Position~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Castling.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Castling.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Castling.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Castling.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Castling.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Castling.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Castling.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Castling~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EnPassant.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EnPassant.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EnPassant.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EnPassant.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EnPassant.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EnPassant.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EnPassant.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EnPassant~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/UndoState.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/UndoState.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/UndoState.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/UndoState.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/UndoState.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/UndoState.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/UndoState.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/UndoState.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/UndoState~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Comparable+Bounded.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Comparable+Bounded.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Comparable+Bounded.dia", "index-unit-output-path": "/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Comparable+Bounded.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Comparable+Bounded.bc", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Comparable+Bounded.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Comparable+Bounded.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Comparable+Bounded~partial.swiftmodule"}}