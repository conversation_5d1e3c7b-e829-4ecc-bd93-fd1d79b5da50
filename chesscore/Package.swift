// swift-tools-version: 6.0

import PackageDescription

let package = Package(
  name: "ChessCore",
  platforms: [
    .iOS(.v13),
    .macCatalyst(.v13),
    .macOS(.v10_15),
    .tvOS(.v13),
    .watchOS(.v6)
  ],
  products: [
    .library(
      name: "ChessCoreFoundation",
      targets: ["ChessCoreFoundation"]
    )
  ],
  dependencies: [
    // Add any external dependencies here if needed
  ],
  targets: [
    .target(
      name: "ChessCoreFoundation",
      dependencies: [],
      path: "Sources/ChessCoreFoundation"
    ),
    .testTarget(
      name: "ChessCoreFoundationTests",
      dependencies: ["ChessCoreFoundation"],
      path: "Tests/ChessCoreFoundationTests"
    )
  ]
)
