//
//  Square.swift
//  ChessCoreFoundation
//

import Foundation

/// Represents a square on the chess board.
public enum Square: Int, CaseIterable, Sendable {
  /// The file on the chess board, from a to h.
  public enum File: String, CaseIterable, Sendable {
    case a, b, c, d, e, f, g, h

    /// The number corresponding to the file.
    ///
    /// For example:
    /// ```
    /// Square.File.a.number // 1
    /// Square.File.h.number // 8
    /// ```
    public var number: Int {
      File.allCases.firstIndex(of: self)! + 1
    }

    /// Initialize a file from a number from 1 through 8.
    ///
    /// - parameter number: The number of the file to set.
    ///
    /// If an invalid number is passed, i.e. less than 1 or
    /// greater than 8, the file is set to `.a`.
    ///
    /// See also `Square.File.number`.
    public init(_ number: Int) {
      switch number {
      case 1: self = .a
      case 2: self = .b
      case 3: self = .c
      case 4: self = .d
      case 5: self = .e
      case 6: self = .f
      case 7: self = .g
      case 8: self = .h
      case let n where n < 1: self = .a
      case let n where n > 8: self = .h
      default: self = .a
      }
    }
  }

  /// The rank on the chess board, from 1 to 8.
  public struct Rank: ExpressibleByIntegerLiteral, Hashable, Sendable {
    /// The possible range of Rank numbers.
    public static let range = 1...8

    /// The integer value of the Rank.
    public var value: Int

    /// Initialize a Rank with a provided integer value.
    public init(_ value: Int) {
      self.value = value.bounded(by: Rank.range)
    }

    /// Initialize a Rank with a provided integer literal.
    public init(integerLiteral value: IntegerLiteralType) {
      self.init(value)
    }
  }

  // MARK: Squares

  case a1, b1, c1, d1, e1, f1, g1, h1
  case a2, b2, c2, d2, e2, f2, g2, h2
  case a3, b3, c3, d3, e3, f3, g3, h3
  case a4, b4, c4, d4, e4, f4, g4, h4
  case a5, b5, c5, d5, e5, f5, g5, h5
  case a6, b6, c6, d6, e6, f6, g6, h6
  case a7, b7, c7, d7, e7, f7, g7, h7
  case a8, b8, c8, d8, e8, f8, g8, h8

  // MARK: Initializer

  /// Initializes a board square from the given notation string.
  ///
  /// - parameter notation: The notation of the square, e.g. `"a1"`.
  ///
  public init(_ notation: String) {
    let file = File.allCases.first { $0.rawValue == notation.prefix(1) } ?? .a
    let rank = Rank(Int(notation.suffix(1)) ?? 1)
    self.init(file, rank)
  }

  /// Initializes a board square from the provided file and rank.
  ///
  /// - parameter file: The file (column) of the square, from `a` to `h`.
  /// - parameter rank: The rank (row) of the square, from `1` to `8`.
  ///
  public init(_ file: File, _ rank: Rank) {
    let fileIndex = file.number - 1
    let rankIndex = rank.value - 1
    let rawValue = rankIndex * 8 + fileIndex
    self = Square.allCases[rawValue]
  }

  // MARK: Components

  /// The file (column) of the given square, from `a` through `h`.
  public var file: File {
    File.allCases[rawValue % 8]
  }

  /// The rank (row) of the given square, from `1` to `8`.
  public var rank: Rank {
    Rank(rawValue / 8 + 1)
  }

  /// The notation for the given square.
  public var notation: String {
    file.rawValue + "\(rank.value)"
  }

  // MARK: Color

  /// Represents the possible colors of each board square.
  public enum Color: CaseIterable, Sendable {
    case light, dark
  }

  /// The color of the square on the board, either light or dark.
  public var color: Color {
    if (file.number % 2 == 0 && rank.value % 2 == 0) || (file.number % 2 != 0 && rank.value % 2 != 0) {
      .dark
    } else {
      .light
    }
  }

  // MARK: Directional

  /// The `Square` to the left of the current one.
  ///
  /// Returns the same square if called from a square on the A file.
  public var left: Square {
    guard file != .a else { return self }
    return Square(File(file.number - 1), rank)
  }

  /// The `Square` to the right of the current one.
  ///
  /// Returns the same square if called from a square on the H file.
  public var right: Square {
    guard file != .h else { return self }
    return Square(File(file.number + 1), rank)
  }

  /// The `Square` above the current one.
  ///
  /// Returns the same square if called from a square on the 8th rank.
  public var up: Square {
    guard rank.value < 8 else { return self }
    return Square(file, Rank(rank.value + 1))
  }

  /// The `Square` below the current one.
  ///
  /// Returns the same square if called from a square on the 1st rank.
  public var down: Square {
    guard rank.value > 1 else { return self }
    return Square(file, Rank(rank.value - 1))
  }
}

// MARK: - Bounded Support
extension Int {
  /// Returns the value bounded by the given range.
  func bounded(by range: ClosedRange<Int>) -> Int {
    return Swift.max(range.lowerBound, Swift.min(range.upperBound, self))
  }
}