//
//  PieceSet.swift
//  ChessCoreFoundation
//

import Foundation

/// Display mode for piece representation in text output.
public enum PieceDisplayMode: Sendable {
  /// Use FEN notation (P, N, B, R, Q, K for white; p, n, b, r, q, k for black)
  case fen
  /// Use Unicode chess symbols (♔♕♖♗♘♙ for white; ♚♛♜♝♞♟ for black)
  case graphic
}

/// Stores the bitboards for all pieces using an efficient bitboard-based representation.
///
/// Also contains convenient amalgamations of different combinations of pieces.
public struct PieceSet: Hashable, Sendable {
  /// Bitboard for black king pieces.
  public var k: Bitboard = 0
  /// Bitboard for black queen pieces.
  public var q: Bitboard = 0
  /// Bitboard for black rook pieces.
  public var r: Bitboard = 0
  /// Bitboard for black bishop pieces.
  public var b: Bitboard = 0
  /// Bitboard for black knight pieces.
  public var n: Bitboard = 0
  /// Bitboard for black pawn pieces.
  public var p: Bitboard = 0

  /// Bitboard for white king pieces.
  public var K: Bitboard = 0
  /// Bitboard for white queen pieces.
  public var Q: Bitboard = 0
  /// Bitboard for white rook pieces.
  public var R: Bitboard = 0
  /// Bitboard for white bishop pieces.
  public var B: Bitboard = 0
  /// Bitboard for white knight pieces.
  public var N: Bitboard = 0
  /// Bitboard for white pawn pieces.
  public var P: Bitboard = 0

  /// Bitboard for all the pieces.
  public var all: Bitboard { black | white }
  /// Bitboard for all the black pieces.
  public var black: Bitboard { k | q | r | b | n | p }
  /// Bitboard for all the white pieces.
  public var white: Bitboard { K | Q | R | B | N | P }

  /// Bitboard for all the king pieces.
  public var kings: Bitboard { k | K }
  /// Bitboard for all the queen pieces.
  public var queens: Bitboard { q | Q }
  /// Bitboard for all the rook pieces.
  public var rooks: Bitboard { r | R }
  /// Bitboard for all the bishop pieces.
  public var bishops: Bitboard { b | B }
  /// Bitboard for all the knight pieces.
  public var knights: Bitboard { n | N }
  /// Bitboard for all the pawn pieces.
  public var pawns: Bitboard { p | P }

  /// Bitboard for all the diagonal sliding pieces.
  public var diagonals: Bitboard { Q | q | B | b }
  /// Bitboard for all the vertical/horizontal sliding pieces.
  public var lines: Bitboard { Q | q | R | r }

  /// Returns all pieces as an array.
  public var pieces: [Piece] {
    var result: [Piece] = []
    
    // Black pieces
    result.append(contentsOf: k.squares.map { Piece(.king, color: .black, square: $0) })
    result.append(contentsOf: q.squares.map { Piece(.queen, color: .black, square: $0) })
    result.append(contentsOf: r.squares.map { Piece(.rook, color: .black, square: $0) })
    result.append(contentsOf: b.squares.map { Piece(.bishop, color: .black, square: $0) })
    result.append(contentsOf: n.squares.map { Piece(.knight, color: .black, square: $0) })
    result.append(contentsOf: p.squares.map { Piece(.pawn, color: .black, square: $0) })
    
    // White pieces
    result.append(contentsOf: K.squares.map { Piece(.king, color: .white, square: $0) })
    result.append(contentsOf: Q.squares.map { Piece(.queen, color: .white, square: $0) })
    result.append(contentsOf: R.squares.map { Piece(.rook, color: .white, square: $0) })
    result.append(contentsOf: B.squares.map { Piece(.bishop, color: .white, square: $0) })
    result.append(contentsOf: N.squares.map { Piece(.knight, color: .white, square: $0) })
    result.append(contentsOf: P.squares.map { Piece(.pawn, color: .white, square: $0) })
    
    return result
  }

  /// Initialize with an array of pieces.
  public init(pieces: [Piece] = []) {
    pieces.forEach { add($0) }
  }

  /// Returns the bitboard for pieces of the given color.
  public func get(_ color: Piece.Color) -> Bitboard {
    switch color {
    case .white: white
    case .black: black
    }
  }

  /// Returns the bitboard for pieces of the given kind.
  public func get(_ kind: Piece.Kind) -> Bitboard {
    switch kind {
    case .pawn: pawns
    case .knight: knights
    case .bishop: bishops
    case .rook: rooks
    case .queen: queens
    case .king: kings
    }
  }

  /// Returns the piece at the given square, if any.
  public func get(_ square: Square) -> Piece? {
    let squareBB = square.bb
    
    // Check black pieces
    if k & squareBB != 0 { return Piece(.king, color: .black, square: square) }
    if q & squareBB != 0 { return Piece(.queen, color: .black, square: square) }
    if r & squareBB != 0 { return Piece(.rook, color: .black, square: square) }
    if b & squareBB != 0 { return Piece(.bishop, color: .black, square: square) }
    if n & squareBB != 0 { return Piece(.knight, color: .black, square: square) }
    if p & squareBB != 0 { return Piece(.pawn, color: .black, square: square) }

    // Check white pieces
    if K & squareBB != 0 { return Piece(.king, color: .white, square: square) }
    if Q & squareBB != 0 { return Piece(.queen, color: .white, square: square) }
    if R & squareBB != 0 { return Piece(.rook, color: .white, square: square) }
    if B & squareBB != 0 { return Piece(.bishop, color: .white, square: square) }
    if N & squareBB != 0 { return Piece(.knight, color: .white, square: square) }
    if P & squareBB != 0 { return Piece(.pawn, color: .white, square: square) }

    return nil
  }

  /// Adds a piece to its current square.
  public mutating func add(_ piece: Piece) {
    add(piece, to: piece.square)
  }

  /// Adds a piece to the specified square.
  public mutating func add(_ piece: Piece, to square: Square) {
    let squareBB = square.bb
    
    switch (piece.color, piece.kind) {
    case (.black, .king): k |= squareBB
    case (.black, .queen): q |= squareBB
    case (.black, .rook): r |= squareBB
    case (.black, .bishop): b |= squareBB
    case (.black, .knight): n |= squareBB
    case (.black, .pawn): p |= squareBB

    case (.white, .king): K |= squareBB
    case (.white, .queen): Q |= squareBB
    case (.white, .rook): R |= squareBB
    case (.white, .bishop): B |= squareBB
    case (.white, .knight): N |= squareBB
    case (.white, .pawn): P |= squareBB
    }
  }

  /// Removes a piece from the board.
  public mutating func remove(_ piece: Piece) {
    let squareBB = ~piece.square.bb
    
    switch (piece.color, piece.kind) {
    case (.black, .king): k &= squareBB
    case (.black, .queen): q &= squareBB
    case (.black, .rook): r &= squareBB
    case (.black, .bishop): b &= squareBB
    case (.black, .knight): n &= squareBB
    case (.black, .pawn): p &= squareBB

    case (.white, .king): K &= squareBB
    case (.white, .queen): Q &= squareBB
    case (.white, .rook): R &= squareBB
    case (.white, .bishop): B &= squareBB
    case (.white, .knight): N &= squareBB
    case (.white, .pawn): P &= squareBB
    }
  }

  /// Replaces a piece's kind with another, such as when performing a piece promotion.
  public mutating func replace(_ kind: Piece.Kind, for piece: Piece) {
    var newPiece = piece
    newPiece.kind = kind

    remove(piece)
    add(newPiece)
  }

  /// Moves a piece from its current square to a new square.
  public mutating func move(_ piece: Piece, to square: Square) {
    remove(piece)
    add(piece, to: square)
  }
}

// MARK: - CustomStringConvertible
extension PieceSet: CustomStringConvertible {
  public var description: String {
    string(displayMode: .fen)
  }
  
  /// Returns a string representation of the piece set using the specified display mode.
  ///
  /// - parameter displayMode: The mode to use for displaying pieces (.fen or .graphic)
  /// - returns: A formatted string showing the board position
  public func string(displayMode: PieceDisplayMode = .fen) -> String {
    var s = ""

    for rank in Square.Rank.range.reversed() {
      s += "\(rank)"

      for file in Square.File.allCases {
        let sq = Square(file, .init(rank))

        if let piece = get(sq) {
          switch displayMode {
          case .fen:
            s += " \(piece.fen)"
          case .graphic:
            s += " \(piece.graphic)"
          }
        } else {
          s += " ·"
        }
      }

      s += "\n"
    }

    return s + "  a b c d e f g h"
  }
}