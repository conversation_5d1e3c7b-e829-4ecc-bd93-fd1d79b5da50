//
//  PieceSetTests.swift
//  ChessCoreFoundationTests
//

@testable import ChessCoreFoundation
import XCTest

final class PieceSetTests: XCTestCase {

  func testEmptyPieceSet() {
    let emptySet = PieceSet()
    XCTAssertEqual(emptySet.all, 0)
    XCTAssertEqual(emptySet.white, 0)
    XCTAssertEqual(emptySet.black, 0)
    XCTAssertEqual(emptySet.pieces.count, 0)
  }

  func testAddPiece() {
    var pieceSet = PieceSet()
    let piece = Piece(.king, color: .white, square: .e1)
    
    pieceSet.add(piece)
    
    XCTAssertEqual(pieceSet.K, Square.e1.bb)
    XCTAssertEqual(pieceSet.white, Square.e1.bb)
    XCTAssertEqual(pieceSet.all, Square.e1.bb)
    XCTAssertEqual(pieceSet.kings, Square.e1.bb)
    XCTAssertEqual(pieceSet.pieces.count, 1)
  }

  func testAddMultiplePieces() {
    var pieceSet = PieceSet()
    let whiteKing = Piece(.king, color: .white, square: .e1)
    let blackKing = Piece(.king, color: .black, square: .e8)
    let whiteQueen = Piece(.queen, color: .white, square: .d1)
    
    pieceSet.add(whiteKing)
    pieceSet.add(blackKing)
    pieceSet.add(whiteQueen)
    
    XCTAssertEqual(pieceSet.K, Square.e1.bb)
    XCTAssertEqual(pieceSet.k, Square.e8.bb)
    XCTAssertEqual(pieceSet.Q, Square.d1.bb)
    XCTAssertEqual(pieceSet.kings, Square.e1.bb | Square.e8.bb)
    XCTAssertEqual(pieceSet.white, Square.e1.bb | Square.d1.bb)
    XCTAssertEqual(pieceSet.black, Square.e8.bb)
    XCTAssertEqual(pieceSet.pieces.count, 3)
  }

  func testRemovePiece() {
    var pieceSet = PieceSet()
    let piece = Piece(.king, color: .white, square: .e1)
    
    pieceSet.add(piece)
    XCTAssertEqual(pieceSet.pieces.count, 1)
    
    pieceSet.remove(piece)
    XCTAssertEqual(pieceSet.K, 0)
    XCTAssertEqual(pieceSet.white, 0)
    XCTAssertEqual(pieceSet.all, 0)
    XCTAssertEqual(pieceSet.pieces.count, 0)
  }

  func testMovePiece() {
    var pieceSet = PieceSet()
    let piece = Piece(.king, color: .white, square: .e1)
    
    pieceSet.add(piece)
    XCTAssertEqual(pieceSet.K, Square.e1.bb)
    
    pieceSet.move(piece, to: .e2)
    XCTAssertEqual(pieceSet.K, Square.e2.bb)
    XCTAssertTrue(pieceSet.K & Square.e1.bb == 0)
  }

  func testReplacePiece() {
    var pieceSet = PieceSet()
    let pawn = Piece(.pawn, color: .white, square: .e7)
    
    pieceSet.add(pawn)
    XCTAssertEqual(pieceSet.P, Square.e7.bb)
    XCTAssertEqual(pieceSet.Q, 0)
    
    pieceSet.replace(.queen, for: pawn)
    XCTAssertEqual(pieceSet.P, 0)
    XCTAssertEqual(pieceSet.Q, Square.e7.bb)
  }

  func testGetPieceByColor() {
    var pieceSet = PieceSet()
    pieceSet.add(Piece(.king, color: .white, square: .e1))
    pieceSet.add(Piece(.queen, color: .white, square: .d1))
    pieceSet.add(Piece(.king, color: .black, square: .e8))
    
    let whitePieces = pieceSet.get(.white)
    let blackPieces = pieceSet.get(.black)
    
    XCTAssertEqual(whitePieces, Square.e1.bb | Square.d1.bb)
    XCTAssertEqual(blackPieces, Square.e8.bb)
  }

  func testGetPieceByKind() {
    var pieceSet = PieceSet()
    pieceSet.add(Piece(.king, color: .white, square: .e1))
    pieceSet.add(Piece(.king, color: .black, square: .e8))
    pieceSet.add(Piece(.queen, color: .white, square: .d1))
    
    let kings = pieceSet.get(.king)
    let queens = pieceSet.get(.queen)
    let pawns = pieceSet.get(.pawn)
    
    XCTAssertEqual(kings, Square.e1.bb | Square.e8.bb)
    XCTAssertEqual(queens, Square.d1.bb)
    XCTAssertEqual(pawns, 0)
  }

  func testGetPieceBySquare() {
    var pieceSet = PieceSet()
    let king = Piece(.king, color: .white, square: .e1)
    pieceSet.add(king)
    
    let retrievedPiece = pieceSet.get(.e1)
    XCTAssertEqual(retrievedPiece?.kind, .king)
    XCTAssertEqual(retrievedPiece?.color, .white)
    XCTAssertEqual(retrievedPiece?.square, .e1)
    
    let emptySquare = pieceSet.get(.e2)
    XCTAssertNil(emptySquare)
  }

  func testInitializationWithPiecesArray() {
    let pieces = [
      Piece(.king, color: .white, square: .e1),
      Piece(.queen, color: .white, square: .d1),
      Piece(.king, color: .black, square: .e8)
    ]
    
    let pieceSet = PieceSet(pieces: pieces)
    
    XCTAssertEqual(pieceSet.pieces.count, 3)
    XCTAssertEqual(pieceSet.K, Square.e1.bb)
    XCTAssertEqual(pieceSet.Q, Square.d1.bb)
    XCTAssertEqual(pieceSet.k, Square.e8.bb)
  }

  func testPieceTypeAggregations() {
    var pieceSet = PieceSet()
    
    // Add various pieces
    pieceSet.add(Piece(.rook, color: .white, square: .a1))
    pieceSet.add(Piece(.rook, color: .black, square: .a8))
    pieceSet.add(Piece(.bishop, color: .white, square: .c1))
    pieceSet.add(Piece(.bishop, color: .black, square: .c8))
    pieceSet.add(Piece(.queen, color: .white, square: .d1))
    pieceSet.add(Piece(.queen, color: .black, square: .d8))
    
    // Test rooks bitboard
    XCTAssertEqual(pieceSet.rooks, Square.a1.bb | Square.a8.bb)
    
    // Test bishops bitboard
    XCTAssertEqual(pieceSet.bishops, Square.c1.bb | Square.c8.bb)
    
    // Test queens bitboard
    XCTAssertEqual(pieceSet.queens, Square.d1.bb | Square.d8.bb)
    
    // Test diagonal sliders (bishops + queens)
    XCTAssertEqual(pieceSet.diagonals, Square.c1.bb | Square.c8.bb | Square.d1.bb | Square.d8.bb)
    
    // Test line sliders (rooks + queens)
    XCTAssertEqual(pieceSet.lines, Square.a1.bb | Square.a8.bb | Square.d1.bb | Square.d8.bb)
  }

  func testPawnHandling() {
    var pieceSet = PieceSet()
    
    // Add white pawns on second rank
    for file in Square.File.allCases {
      let square = Square(file, Square.Rank(2))
      pieceSet.add(Piece(.pawn, color: .white, square: square))
    }
    
    // Add black pawns on seventh rank
    for file in Square.File.allCases {
      let square = Square(file, Square.Rank(7))
      pieceSet.add(Piece(.pawn, color: .black, square: square))
    }
    
    XCTAssertEqual(pieceSet.P, Bitboard.rank1.north())  // Second rank
    XCTAssertEqual(pieceSet.p, Bitboard.rank8.south())  // Seventh rank
    XCTAssertEqual(pieceSet.pawns, pieceSet.P | pieceSet.p)
  }

  func testKnightHandling() {
    var pieceSet = PieceSet()
    
    pieceSet.add(Piece(.knight, color: .white, square: .b1))
    pieceSet.add(Piece(.knight, color: .white, square: .g1))
    pieceSet.add(Piece(.knight, color: .black, square: .b8))
    pieceSet.add(Piece(.knight, color: .black, square: .g8))
    
    let expectedKnights = Square.b1.bb | Square.g1.bb | Square.b8.bb | Square.g8.bb
    XCTAssertEqual(pieceSet.knights, expectedKnights)
    XCTAssertEqual(pieceSet.N, Square.b1.bb | Square.g1.bb)
    XCTAssertEqual(pieceSet.n, Square.b8.bb | Square.g8.bb)
  }

  func testPieceSetDescription() {
    var pieceSet = PieceSet()
    pieceSet.add(Piece(.king, color: .white, square: .e1))
    pieceSet.add(Piece(.king, color: .black, square: .e8))
    
    let description = pieceSet.description
    
    // Should contain rank numbers
    XCTAssertTrue(description.contains("8"))
    XCTAssertTrue(description.contains("1"))
    
    // Should contain file letters
    XCTAssertTrue(description.contains("a b c d e f g h"))
    
    // Should contain piece representations
    XCTAssertTrue(description.contains("K"))  // White king
    XCTAssertTrue(description.contains("k"))  // Black king
    
    // Should contain empty square markers
    XCTAssertTrue(description.contains("·"))
  }

  func testPieceSetDisplayModes() {
    var pieceSet = PieceSet()
    pieceSet.add(Piece(.king, color: .white, square: .e1))
    pieceSet.add(Piece(.queen, color: .black, square: .d8))

    // Test FEN mode
    let fenDescription = pieceSet.string(displayMode: .fen)
    XCTAssertTrue(fenDescription.contains("K"))   // White king FEN
    XCTAssertTrue(fenDescription.contains("q"))   // Black queen FEN
    XCTAssertFalse(fenDescription.contains("♔"))  // Should not contain graphics

    // Test graphic mode
    let graphicDescription = pieceSet.string(displayMode: .graphic)
    XCTAssertTrue(graphicDescription.contains("♔"))  // White king graphic
    XCTAssertTrue(graphicDescription.contains("♛"))  // Black queen graphic
    XCTAssertFalse(graphicDescription.contains("K")) // Should not contain FEN (except in file labels)
    XCTAssertFalse(graphicDescription.contains("q")) // Should not contain FEN

    // Test that default description uses FEN mode
    XCTAssertEqual(pieceSet.description, fenDescription)
  }

  func testDisplayModeEnum() {
    // Test that the enum cases exist and are accessible
    let fenMode = PieceDisplayMode.fen
    let graphicMode = PieceDisplayMode.graphic
    
    XCTAssertNotEqual(fenMode, graphicMode)
    
    // Test in a switch to ensure exhaustiveness
    func testDisplayMode(_ mode: PieceDisplayMode) -> String {
      switch mode {
      case .fen: return "fen"
      case .graphic: return "graphic"
      }
    }
    
    XCTAssertEqual(testDisplayMode(.fen), "fen")
    XCTAssertEqual(testDisplayMode(.graphic), "graphic")
  }

  func testAddToSpecificSquare() {
    var pieceSet = PieceSet()
    let king = Piece(.king, color: .white, square: .e1)
    
    // Add the king to a different square than its current square
    pieceSet.add(king, to: .e2)
    
    // Should be added to the specified square, not the piece's square
    XCTAssertEqual(pieceSet.K, Square.e2.bb)
    XCTAssertTrue(pieceSet.K & Square.e1.bb == 0)
  }

  func testStandardStartingPosition() {
    var pieceSet = PieceSet()
    
    // Add all pieces for standard starting position
    let startingPieces = [
      // White pieces
      Piece(.rook, color: .white, square: .a1),
      Piece(.knight, color: .white, square: .b1),
      Piece(.bishop, color: .white, square: .c1),
      Piece(.queen, color: .white, square: .d1),
      Piece(.king, color: .white, square: .e1),
      Piece(.bishop, color: .white, square: .f1),
      Piece(.knight, color: .white, square: .g1),
      Piece(.rook, color: .white, square: .h1),
      
      // Black pieces
      Piece(.rook, color: .black, square: .a8),
      Piece(.knight, color: .black, square: .b8),
      Piece(.bishop, color: .black, square: .c8),
      Piece(.queen, color: .black, square: .d8),
      Piece(.king, color: .black, square: .e8),
      Piece(.bishop, color: .black, square: .f8),
      Piece(.knight, color: .black, square: .g8),
      Piece(.rook, color: .black, square: .h8)
    ]
    
    // Add white pawns
    for file in Square.File.allCases {
      startingPieces.append(Piece(.pawn, color: .white, square: Square(file, Square.Rank(2))))
    }
    
    // Add black pawns
    for file in Square.File.allCases {
      startingPieces.append(Piece(.pawn, color: .black, square: Square(file, Square.Rank(7))))
    }
    
    pieceSet = PieceSet(pieces: startingPieces)
    
    // Verify total piece count
    XCTAssertEqual(pieceSet.pieces.count, 32)
    
    // Verify piece placement
    XCTAssertEqual(pieceSet.white.nonzeroBitCount, 16)
    XCTAssertEqual(pieceSet.black.nonzeroBitCount, 16)
    XCTAssertEqual(pieceSet.all.nonzeroBitCount, 32)
    
    // Verify specific pieces
    XCTAssertEqual(pieceSet.K, Square.e1.bb)
    XCTAssertEqual(pieceSet.k, Square.e8.bb)
    XCTAssertEqual(pieceSet.Q, Square.d1.bb)
    XCTAssertEqual(pieceSet.q, Square.d8.bb)
  }

  func testPerformanceAddRemove() {
    let pieces = [
      Piece(.king, color: .white, square: .e1),
      Piece(.queen, color: .white, square: .d1),
      Piece(.king, color: .black, square: .e8),
      Piece(.queen, color: .black, square: .d8)
    ]
    
    measure {
      for _ in 0..<10000 {
        var pieceSet = PieceSet()
        for piece in pieces {
          pieceSet.add(piece)
        }
        for piece in pieces {
          pieceSet.remove(piece)
        }
      }
    }
  }

  func testPerformanceGetPiece() {
    var pieceSet = PieceSet()
    let squares = [Square.e1, .d1, .e8, .d8, .a1, .h1, .a8, .h8]
    
    for square in squares {
      pieceSet.add(Piece(.king, color: .white, square: square))
    }
    
    measure {
      for _ in 0..<100000 {
        for square in squares {
          _ = pieceSet.get(square)
        }
      }
    }
  }

  func testHashableConformance() {
    let pieces = [
      Piece(.king, color: .white, square: .e1),
      Piece(.queen, color: .white, square: .d1)
    ]
    
    let pieceSet1 = PieceSet(pieces: pieces)
    let pieceSet2 = PieceSet(pieces: pieces)
    
    XCTAssertEqual(pieceSet1, pieceSet2)
    
    let set: Set<PieceSet> = [pieceSet1, pieceSet2]
    XCTAssertEqual(set.count, 1)  // Should be considered the same
  }
}