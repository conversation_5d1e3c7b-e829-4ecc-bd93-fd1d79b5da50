//
//  AttacksTests.swift
//  ChessCoreFoundationTests
//

@testable import ChessCoreFoundation
import XCTest

final class AttacksTests: XCTestCase {

  override func setUpWithError() throws {
    // Initialize attacks before running tests
    Attacks.create()
  }

  func testKingAttacks() {
    let e4Attacks = Attacks.kings[Square.e4.bb] ?? 0
    let expectedSquares: [Square] = [.d3, .d4, .d5, .e3, .e5, .f3, .f4, .f5]
    let expectedBitboard = expectedSquares.bb
    
    XCTAssertEqual(e4Attacks, expectedBitboard)
    XCTAssertEqual(e4Attacks.squares.count, 8)
  }

  func testKingAttacksCorners() {
    // Test corner squares have fewer attacks
    let a1Attacks = Attacks.kings[Square.a1.bb] ?? 0
    XCTAssertEqual(a1Attacks.squares.count, 3)
    XCTAssertTrue(a1Attacks.squares.contains(.a2))
    XCTAssertTrue(a1Attacks.squares.contains(.b1))
    XCTAssertTrue(a1Attacks.squares.contains(.b2))
    
    let h8Attacks = Attacks.kings[Square.h8.bb] ?? 0
    XCTAssertEqual(h8Attacks.squares.count, 3)
    XCTAssertTrue(h8Attacks.squares.contains(.g8))
    XCTAssertTrue(h8Attacks.squares.contains(.h7))
    XCTAssertTrue(h8Attacks.squares.contains(.g7))
  }

  func testKingAttacksEdges() {
    // Test edge squares have 5 attacks
    let e1Attacks = Attacks.kings[Square.e1.bb] ?? 0
    XCTAssertEqual(e1Attacks.squares.count, 5)
    
    let a4Attacks = Attacks.kings[Square.a4.bb] ?? 0
    XCTAssertEqual(a4Attacks.squares.count, 5)
  }

  func testKnightAttacks() {
    let e4Attacks = Attacks.knights[Square.e4.bb] ?? 0
    let expectedSquares: [Square] = [.c3, .c5, .d2, .d6, .f2, .f6, .g3, .g5]
    let expectedBitboard = expectedSquares.bb
    
    XCTAssertEqual(e4Attacks, expectedBitboard)
    XCTAssertEqual(e4Attacks.squares.count, 8)
  }

  func testKnightAttacksCorners() {
    // Test corner squares have 2 knight moves
    let a1Attacks = Attacks.knights[Square.a1.bb] ?? 0
    XCTAssertEqual(a1Attacks.squares.count, 2)
    XCTAssertTrue(a1Attacks.squares.contains(.b3))
    XCTAssertTrue(a1Attacks.squares.contains(.c2))
    
    let h8Attacks = Attacks.knights[Square.h8.bb] ?? 0
    XCTAssertEqual(h8Attacks.squares.count, 2)
    XCTAssertTrue(h8Attacks.squares.contains(.f7))
    XCTAssertTrue(h8Attacks.squares.contains(.g6))
  }

  func testKnightAttacksEdges() {
    // Test edge squares have limited knight moves
    let a4Attacks = Attacks.knights[Square.a4.bb] ?? 0
    XCTAssertEqual(a4Attacks.squares.count, 4)
    
    let e1Attacks = Attacks.knights[Square.e1.bb] ?? 0
    XCTAssertEqual(e1Attacks.squares.count, 4)
  }

  func testBishopAttacksEmptyBoard() {
    let e4Attacks = Attacks.bishops.attacks(from: .e4, for: 0)
    
    // On empty board, bishop should attack entire diagonals
    let expectedSquares: [Square] = [
      // Main diagonal (a8-h1)
      .d3, .c2, .b1, .f5, .g6, .h7,
      // Anti-diagonal (h8-a1)
      .d5, .c6, .b7, .a8, .f3, .g2, .h1
    ]
    
    for square in expectedSquares {
      XCTAssertTrue(e4Attacks & square.bb != 0, "Bishop should attack \(square)")
    }
    
    // Should not attack squares on same rank or file
    XCTAssertTrue(e4Attacks & Square.e1.bb == 0)  // Same file
    XCTAssertTrue(e4Attacks & Square.a4.bb == 0)  // Same rank
  }

  func testBishopAttacksWithBlockers() {
    // Place a blocker on f5
    let blockerBitboard = Square.f5.bb
    let e4Attacks = Attacks.bishops.attacks(from: .e4, for: blockerBitboard)
    
    // Should attack f5 (the blocker) but not beyond
    XCTAssertTrue(e4Attacks & Square.f5.bb != 0)
    XCTAssertTrue(e4Attacks & Square.g6.bb == 0)
    XCTAssertTrue(e4Attacks & Square.h7.bb == 0)
    
    // Other directions should be unaffected
    XCTAssertTrue(e4Attacks & Square.d3.bb != 0)
    XCTAssertTrue(e4Attacks & Square.c2.bb != 0)
  }

  func testRookAttacksEmptyBoard() {
    let e4Attacks = Attacks.rooks.attacks(from: .e4, for: 0)
    
    // Should attack entire rank and file
    let expectedSquares: [Square] = [
      // File attacks
      .e1, .e2, .e3, .e5, .e6, .e7, .e8,
      // Rank attacks
      .a4, .b4, .c4, .d4, .f4, .g4, .h4
    ]
    
    for square in expectedSquares {
      XCTAssertTrue(e4Attacks & square.bb != 0, "Rook should attack \(square)")
    }
    
    // Should not attack diagonal squares
    XCTAssertTrue(e4Attacks & Square.d3.bb == 0)
    XCTAssertTrue(e4Attacks & Square.f5.bb == 0)
  }

  func testRookAttacksWithBlockers() {
    // Place blockers on e6 and g4
    let blockerBitboard = Square.e6.bb | Square.g4.bb
    let e4Attacks = Attacks.rooks.attacks(from: .e4, for: blockerBitboard)
    
    // Should attack blockers but not beyond
    XCTAssertTrue(e4Attacks & Square.e6.bb != 0)
    XCTAssertTrue(e4Attacks & Square.e7.bb == 0)
    XCTAssertTrue(e4Attacks & Square.e8.bb == 0)
    
    XCTAssertTrue(e4Attacks & Square.g4.bb != 0)
    XCTAssertTrue(e4Attacks & Square.h4.bb == 0)
    
    // Unblocked directions should be full
    XCTAssertTrue(e4Attacks & Square.e1.bb != 0)
    XCTAssertTrue(e4Attacks & Square.a4.bb != 0)
  }

  func testAttacksInitializationIdempotent() {
    // Test that calling create() multiple times doesn't break anything
    let initialKingsCount = Attacks.kings.count
    let initialKnightsCount = Attacks.knights.count
    let initialRooksCount = Attacks.rooks.count
    let initialBishopsCount = Attacks.bishops.count
    
    Attacks.create()
    
    XCTAssertEqual(Attacks.kings.count, initialKingsCount)
    XCTAssertEqual(Attacks.knights.count, initialKnightsCount)
    XCTAssertEqual(Attacks.rooks.count, initialRooksCount)
    XCTAssertEqual(Attacks.bishops.count, initialBishopsCount)
  }

  func testMagicAttacksConsistency() {
    // Test that magic attacks are consistent across multiple calls
    let square = Square.d4
    let occupancy: Bitboard = Square.e4.bb | Square.d7.bb
    
    let attacks1 = Attacks.rooks.attacks(from: square, for: occupancy)
    let attacks2 = Attacks.rooks.attacks(from: square, for: occupancy)
    
    XCTAssertEqual(attacks1, attacks2)
  }

  func testAllSquaresHaveAttacks() {
    // Test that all squares have attack patterns defined
    for square in Square.allCases {
      let kingAttacks = Attacks.kings[square.bb]
      XCTAssertNotNil(kingAttacks, "King attacks not found for \(square)")
      
      let knightAttacks = Attacks.knights[square.bb]
      XCTAssertNotNil(knightAttacks, "Knight attacks not found for \(square)")
      
      // Test that magic attacks can be computed for all squares
      let rookAttacks = Attacks.rooks.attacks(from: square, for: 0)
      XCTAssertGreaterThan(rookAttacks, 0, "Rook attacks empty for \(square)")
      
      let bishopAttacks = Attacks.bishops.attacks(from: square, for: 0)
      XCTAssertGreaterThanOrEqual(bishopAttacks, 0, "Bishop attacks invalid for \(square)")
    }
  }

  func testCornerSquareBishopAttacks() {
    // Test that corner squares have appropriate bishop attacks
    let a1Attacks = Attacks.bishops.attacks(from: .a1, for: 0)
    let expectedA1Squares: [Square] = [.b2, .c3, .d4, .e5, .f6, .g7, .h8]
    
    for square in expectedA1Squares {
      XCTAssertTrue(a1Attacks & square.bb != 0, "Bishop from a1 should attack \(square)")
    }
    
    XCTAssertEqual(a1Attacks.squares.count, 7)
  }

  func testMagicBoundaryValues() {
    // Test with extreme occupancy patterns
    let allOccupied = ~Bitboard(0)
    let e4RookAttacks = Attacks.rooks.attacks(from: .e4, for: allOccupied)
    
    // With all squares occupied, should only attack adjacent squares
    let expectedAttacks: [Square] = [.e3, .e5, .d4, .f4]
    for square in expectedAttacks {
      XCTAssertTrue(e4RookAttacks & square.bb != 0, "Rook should attack adjacent \(square)")
    }
    
    // Should not attack distant squares
    XCTAssertTrue(e4RookAttacks & Square.e1.bb == 0)
    XCTAssertTrue(e4RookAttacks & Square.a4.bb == 0)
  }

  func testAttacksPerformance() {
    let squares = [Square.e4, .d4, .e5, .d5, .c4, .f4, .e3, .d3]
    let occupancies: [Bitboard] = [0, Square.e5.bb, Square.d5.bb | Square.f4.bb]
    
    measure {
      for _ in 0..<10000 {
        for square in squares {
          for occupancy in occupancies {
            _ = Attacks.rooks.attacks(from: square, for: occupancy)
            _ = Attacks.bishops.attacks(from: square, for: occupancy)
          }
        }
      }
    }
  }

  func testKingAttacksPerformance() {
    let squares = Square.allCases
    
    measure {
      for _ in 0..<100000 {
        for square in squares {
          _ = Attacks.kings[square.bb]
        }
      }
    }
  }

  func testKnightAttacksPerformance() {
    let squares = Square.allCases
    
    measure {
      for _ in 0..<100000 {
        for square in squares {
          _ = Attacks.knights[square.bb]
        }
      }
    }
  }

  func testRookMagicValidity() {
    // Test that rook magic numbers produce valid attack patterns
    for square in Square.allCases {
      let emptyBoardAttacks = Attacks.rooks.attacks(from: square, for: 0)
      
      // Should attack same rank and file
      XCTAssertTrue(emptyBoardAttacks & square.file.bb != 0)
      XCTAssertTrue(emptyBoardAttacks & square.rank.bb != 0)
      
      // Should not attack the square itself
      XCTAssertTrue(emptyBoardAttacks & square.bb == 0)
    }
  }

  func testBishopMagicValidity() {
    // Test that bishop magic numbers produce valid attack patterns
    for square in Square.allCases {
      let emptyBoardAttacks = Attacks.bishops.attacks(from: square, for: 0)
      
      // Should not attack same rank or file (except for diagonals)
      let rankAndFile = (square.rank.bb | square.file.bb) & ~square.bb
      let invalidAttacks = emptyBoardAttacks & rankAndFile
      XCTAssertEqual(invalidAttacks, 0, "Bishop from \(square) should not attack rank/file squares")
      
      // Should not attack the square itself
      XCTAssertTrue(emptyBoardAttacks & square.bb == 0)
    }
  }
}