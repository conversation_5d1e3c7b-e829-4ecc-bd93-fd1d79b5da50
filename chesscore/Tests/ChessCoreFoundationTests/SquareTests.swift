//
//  SquareTests.swift
//  ChessCoreFoundationTests
//

@testable import ChessCoreFoundation
import XCTest

final class SquareTests: XCTestCase {

  func testNotation() {
    XCTAssertEqual(Square.a1.notation, "a1")
    XCTAssertEqual(Square.h1.notation, "h1")
    XCTAssertEqual(Square.a8.notation, "a8")
    XCTAssertEqual(Square.h8.notation, "h8")

    XCTAssertEqual(Square("a1"), .a1)
    XCTAssertEqual(Square("h1"), .h1)
    XCTAssertEqual(Square("a8"), .a8)
    XCTAssertEqual(Square("h8"), .h8)
  }

  func testInvalidNotation() {
    XCTAssertEqual(Square("invalid"), .a1)
    XCTAssertEqual(Square(""), .a1)
    XCTAssertEqual(Square("z9"), .a1)
  }

  func testSquareColor() {
    XCTAssertEqual(Square.a1.color, .dark)
    XCTAssertEqual(Square.h1.color, .light)
    XCTAssertEqual(Square.a8.color, .light)
    XCTAssertEqual(Square.h8.color, .dark)
    
    // Test pattern consistency
    XCTAssertEqual(Square.b1.color, .light)
    XCTAssertEqual(Square.c1.color, .dark)
    XCTAssertEqual(Square.d1.color, .light)
    XCTAssertEqual(Square.e1.color, .dark)
  }

  func testFileNumber() {
    XCTAssertEqual(Square.File.a.number, 1)
    XCTAssertEqual(Square.File.b.number, 2)
    XCTAssertEqual(Square.File.c.number, 3)
    XCTAssertEqual(Square.File.d.number, 4)
    XCTAssertEqual(Square.File.e.number, 5)
    XCTAssertEqual(Square.File.f.number, 6)
    XCTAssertEqual(Square.File.g.number, 7)
    XCTAssertEqual(Square.File.h.number, 8)

    XCTAssertEqual(Square.File(1), .a)
    XCTAssertEqual(Square.File(2), .b)
    XCTAssertEqual(Square.File(3), .c)
    XCTAssertEqual(Square.File(4), .d)
    XCTAssertEqual(Square.File(5), .e)
    XCTAssertEqual(Square.File(6), .f)
    XCTAssertEqual(Square.File(7), .g)
    XCTAssertEqual(Square.File(8), .h)
  }

  func testInvalidFileNumber() {
    XCTAssertEqual(Square.File(-10), .a)
    XCTAssertEqual(Square.File(0), .a)
    XCTAssertEqual(Square.File(9), .h)
    XCTAssertEqual(Square.File(100), .h)
  }

  func testRankValue() {
    let rank1: Square.Rank = 1
    let rank8: Square.Rank = 8
    
    XCTAssertEqual(rank1.value, 1)
    XCTAssertEqual(rank8.value, 8)
    
    // Test bounded behavior
    let tooLow = Square.Rank(-5)
    let tooHigh = Square.Rank(15)
    XCTAssertEqual(tooLow.value, 1)
    XCTAssertEqual(tooHigh.value, 8)
  }

  func testDirectionalSquares() {
    // Test left movement
    XCTAssertEqual(Square.a1.left, .a1)  // Can't go left from A file
    XCTAssertEqual(Square.b1.left, .a1)
    XCTAssertEqual(Square.h1.left, .g1)

    // Test right movement
    XCTAssertEqual(Square.a1.right, .b1)
    XCTAssertEqual(Square.g1.right, .h1)
    XCTAssertEqual(Square.h1.right, .h1)  // Can't go right from H file

    // Test up movement
    XCTAssertEqual(Square.a8.up, .a8)    // Can't go up from 8th rank
    XCTAssertEqual(Square.a7.up, .a8)
    XCTAssertEqual(Square.a1.up, .a2)

    // Test down movement
    XCTAssertEqual(Square.a1.down, .a1)  // Can't go down from 1st rank
    XCTAssertEqual(Square.a2.down, .a1)
    XCTAssertEqual(Square.a8.down, .a7)
  }

  func testSquareComponents() {
    XCTAssertEqual(Square.e4.file, .e)
    XCTAssertEqual(Square.e4.rank.value, 4)
    
    XCTAssertEqual(Square.a1.file, .a)
    XCTAssertEqual(Square.a1.rank.value, 1)
    
    XCTAssertEqual(Square.h8.file, .h)
    XCTAssertEqual(Square.h8.rank.value, 8)
  }

  func testSquareInitialization() {
    // Test file-rank initialization
    let square = Square(.e, Square.Rank(4))
    XCTAssertEqual(square, .e4)
    
    let cornerSquare = Square(.h, Square.Rank(8))
    XCTAssertEqual(cornerSquare, .h8)
  }

  func testSquareEnumeration() {
    // Test that all 64 squares are defined
    XCTAssertEqual(Square.allCases.count, 64)
    
    // Test specific squares exist
    XCTAssertTrue(Square.allCases.contains(.a1))
    XCTAssertTrue(Square.allCases.contains(.h8))
    XCTAssertTrue(Square.allCases.contains(.e4))
    XCTAssertTrue(Square.allCases.contains(.d5))
  }

  func testBitboardConversion() {
    // Test that each square has a unique bitboard representation
    let bitboards = Square.allCases.map { $0.bb }
    let uniqueBitboards = Set(bitboards)
    XCTAssertEqual(bitboards.count, uniqueBitboards.count)
    
    // Test specific bitboard values
    XCTAssertEqual(Square.a1.bb, 1)  // First bit
    XCTAssertEqual(Square.b1.bb, 2)  // Second bit
    XCTAssertEqual(Square.h8.bb, 1 << 63)  // Last bit
  }

  func testBitboardToSquareConversion() {
    // Test single-bit bitboards
    XCTAssertEqual(Square(1), .a1)
    XCTAssertEqual(Square(2), .b1)
    XCTAssertEqual(Square(1 << 63), .h8)
    
    // Test invalid bitboards
    XCTAssertNil(Square(0))  // No bits set
    XCTAssertNil(Square(3))  // Multiple bits set
  }

  func testFileAndRankBitboards() {
    // Test file bitboards
    XCTAssertEqual(Square.File.a.bb, Bitboard.aFile)
    XCTAssertEqual(Square.File.h.bb, Bitboard.hFile)
    
    // Test rank bitboards
    XCTAssertEqual(Square.Rank(1).bb, Bitboard.rank1)
    XCTAssertEqual(Square.Rank(8).bb, Bitboard.rank8)
  }

  func testSquareArrayToBitboard() {
    let squares: [Square] = [.a1, .b1, .c1]
    let expectedBitboard: Bitboard = Square.a1.bb | Square.b1.bb | Square.c1.bb
    XCTAssertEqual(squares.bb, expectedBitboard)
    
    let emptySquares: [Square] = []
    XCTAssertEqual(emptySquares.bb, 0)
  }

  func testPerformanceSquareNotation() {
    measure {
      for _ in 0..<10000 {
        _ = Square.e4.notation
      }
    }
  }

  func testPerformanceSquareInit() {
    measure {
      for _ in 0..<10000 {
        _ = Square("e4")
      }
    }
  }
}