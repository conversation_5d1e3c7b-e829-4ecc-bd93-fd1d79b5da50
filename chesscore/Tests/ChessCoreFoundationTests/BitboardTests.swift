//
//  BitboardTests.swift
//  ChessCoreFoundationTests
//

@testable import ChessCoreFoundation
import XCTest

final class BitboardTests: XCTestCase {

  func testBasicBitboardConstants() {
    XCTAssertEqual(Bitboard.aFile, 0x0101010101010101)
    XCTAssertEqual(Bitboard.hFile, 0x8080808080808080)
    XCTAssertEqual(Bitboard.rank1, 0xFF)
    XCTAssertEqual(Bitboard.rank8, 0xFF00000000000000)
    XCTAssertEqual(Bitboard.dark, 0xAA55AA55AA55AA55)
    XCTAssertEqual(Bitboard.light, ~Bitboard.dark)
  }

  func testDirectionalMovement() {
    let d4: Bitboard = Square.d4.bb
    
    // Test single step movements
    XCTAssertEqual(d4.east(), Square.e4.bb)
    XCTAssertEqual(d4.west(), Square.c4.bb)
    XCTAssertEqual(d4.north(), Square.d5.bb)
    XCTAssertEqual(d4.south(), Square.d3.bb)
    
    // Test diagonal movements
    XCTAssertEqual(d4.northEast(), Square.e5.bb)
    XCTAssertEqual(d4.northWest(), Square.c5.bb)
    XCTAssertEqual(d4.southEast(), Square.e3.bb)
    XCTAssertEqual(d4.southWest(), Square.c3.bb)
  }

  func testMultiStepMovement() {
    let d4: Bitboard = Square.d4.bb
    
    // Test multi-step movements
    XCTAssertEqual(d4.east(2), Square.f4.bb)
    XCTAssertEqual(d4.west(2), Square.b4.bb)
    XCTAssertEqual(d4.north(2), Square.d6.bb)
    XCTAssertEqual(d4.south(2), Square.d2.bb)
  }

  func testEdgeHandling() {
    // Test that movements off the board result in 0
    let a1: Bitboard = Square.a1.bb
    let h8: Bitboard = Square.h8.bb
    
    XCTAssertEqual(a1.west(), 0)
    XCTAssertEqual(a1.south(), 0)
    XCTAssertEqual(h8.east(), 0)
    XCTAssertEqual(h8.north(), 0)
  }

  func testBitboardToSquares() {
    // Test single square
    let singleSquare: Bitboard = Square.e4.bb
    let squares = singleSquare.squares
    XCTAssertEqual(squares.count, 1)
    XCTAssertEqual(squares.first, .e4)
    
    // Test multiple squares
    let multipleSquares: Bitboard = Square.a1.bb | Square.h8.bb | Square.e4.bb
    let resultSquares = multipleSquares.squares
    XCTAssertEqual(resultSquares.count, 3)
    XCTAssertTrue(resultSquares.contains(.a1))
    XCTAssertTrue(resultSquares.contains(.h8))
    XCTAssertTrue(resultSquares.contains(.e4))
    
    // Test empty bitboard
    let empty: Bitboard = 0
    XCTAssertEqual(empty.squares.count, 0)
  }

  func testChessStringRepresentation() {
    let centerSquares: Bitboard = Square.d4.bb | Square.e4.bb | Square.d5.bb | Square.e5.bb
    let chessString = centerSquares.chessString()
    
    // Should contain rank and file labels
    XCTAssertTrue(chessString.contains("a b c d e f g h"))
    XCTAssertTrue(chessString.contains("8"))
    XCTAssertTrue(chessString.contains("1"))
    
    // Should show occupied squares
    XCTAssertTrue(chessString.contains("⨯"))
    XCTAssertTrue(chessString.contains("·"))
  }

  func testChessStringCustomCharacters() {
    let square: Bitboard = Square.e4.bb
    let customString = square.chessString("X", ".", labelRanks: false, labelFiles: false)
    
    XCTAssertTrue(customString.contains("X"))
    XCTAssertTrue(customString.contains("."))
    XCTAssertFalse(customString.contains("a b c d e f g h"))
    XCTAssertFalse(customString.contains("8"))
  }

  func testSquareToBitboard() {
    // Test individual squares
    XCTAssertEqual(Square.a1.bb, 1)
    XCTAssertEqual(Square.b1.bb, 2)
    XCTAssertEqual(Square.a2.bb, 256)
    XCTAssertEqual(Square.h8.bb, 1 << 63)
    
    // Test that all squares have unique bitboards
    let allBitboards = Square.allCases.map { $0.bb }
    let uniqueBitboards = Set(allBitboards)
    XCTAssertEqual(allBitboards.count, uniqueBitboards.count)
  }

  func testSquareArrayToBitboard() {
    let squares: [Square] = [.a1, .e4, .h8]
    let expectedBitboard = Square.a1.bb | Square.e4.bb | Square.h8.bb
    XCTAssertEqual(squares.bb, expectedBitboard)
    
    let emptyArray: [Square] = []
    XCTAssertEqual(emptyArray.bb, 0)
    
    let singleSquare: [Square] = [.d4]
    XCTAssertEqual(singleSquare.bb, Square.d4.bb)
  }

  func testBitboardToSquareConversion() {
    // Test valid single-bit bitboards
    XCTAssertEqual(Square(Square.a1.bb), .a1)
    XCTAssertEqual(Square(Square.e4.bb), .e4)
    XCTAssertEqual(Square(Square.h8.bb), .h8)
    
    // Test invalid bitboards
    XCTAssertNil(Square(0))  // No bits set
    XCTAssertNil(Square(3))  // Multiple bits set (a1 and b1)
    XCTAssertNil(Square(Square.a1.bb | Square.h8.bb))  // Multiple bits set
  }

  func testFileBitboards() {
    XCTAssertEqual(Square.File.a.bb, Bitboard.aFile)
    XCTAssertEqual(Square.File.h.bb, Bitboard.hFile)
    
    // Test that file bitboards are correctly positioned
    let bFile = Square.File.b.bb
    XCTAssertTrue(bFile & Square.b1.bb != 0)
    XCTAssertTrue(bFile & Square.b8.bb != 0)
    XCTAssertTrue(bFile & Square.a1.bb == 0)
    XCTAssertTrue(bFile & Square.c1.bb == 0)
  }

  func testRankBitboards() {
    XCTAssertEqual(Square.Rank(1).bb, Bitboard.rank1)
    XCTAssertEqual(Square.Rank(8).bb, Bitboard.rank8)
    
    // Test that rank bitboards are correctly positioned
    let rank4 = Square.Rank(4).bb
    XCTAssertTrue(rank4 & Square.a4.bb != 0)
    XCTAssertTrue(rank4 & Square.h4.bb != 0)
    XCTAssertTrue(rank4 & Square.a3.bb == 0)
    XCTAssertTrue(rank4 & Square.a5.bb == 0)
  }

  func testBitboardPerformance() {
    let testBitboard: Bitboard = 0x00FF00000000FF00
    
    measure {
      for _ in 0..<10000 {
        _ = testBitboard.squares
      }
    }
  }

  func testDirectionalPerformance() {
    let testBitboard: Bitboard = Square.d4.bb
    
    measure {
      for _ in 0..<100000 {
        _ = testBitboard.north().east().south().west()
      }
    }
  }

  func testBitboardOperations() {
    let a1 = Square.a1.bb
    let h8 = Square.h8.bb
    
    // Test OR operation
    let combined = a1 | h8
    XCTAssertEqual(combined.squares.count, 2)
    XCTAssertTrue(combined.squares.contains(.a1))
    XCTAssertTrue(combined.squares.contains(.h8))
    
    // Test AND operation
    XCTAssertEqual(a1 & h8, 0)
    XCTAssertEqual(a1 & a1, a1)
    
    // Test XOR operation
    XCTAssertEqual(a1 ^ h8, combined)
    XCTAssertEqual(a1 ^ a1, 0)
    
    // Test NOT operation
    let notA1 = ~a1
    XCTAssertTrue(notA1 & a1 == 0)
    XCTAssertTrue(notA1 & h8 != 0)
  }

  func testBitboardShifts() {
    let a1 = Square.a1.bb
    
    // Test left shift
    XCTAssertEqual(a1 << 1, Square.b1.bb)
    XCTAssertEqual(a1 << 8, Square.a2.bb)
    
    // Test right shift
    let b1 = Square.b1.bb
    XCTAssertEqual(b1 >> 1, Square.a1.bb)
    
    let a2 = Square.a2.bb
    XCTAssertEqual(a2 >> 8, Square.a1.bb)
  }

  func testSquareColorBitboards() {
    // Test that dark and light squares don't overlap
    XCTAssertEqual(Bitboard.dark & Bitboard.light, 0)
    
    // Test that dark and light squares cover the entire board
    XCTAssertEqual(Bitboard.dark | Bitboard.light, ~Bitboard(0))
    
    // Test specific square colors
    XCTAssertTrue(Bitboard.dark & Square.a1.bb != 0)
    XCTAssertTrue(Bitboard.light & Square.b1.bb != 0)
    XCTAssertTrue(Bitboard.light & Square.a8.bb != 0)
    XCTAssertTrue(Bitboard.dark & Square.h8.bb != 0)
  }

  func testFileEdgeHandling() {
    // Test that westward movement from A-file produces 0
    let aFile = Bitboard.aFile
    XCTAssertEqual(aFile.west(), 0)
    
    // Test that eastward movement from H-file produces 0
    let hFile = Bitboard.hFile
    XCTAssertEqual(hFile.east(), 0)
  }

  func testComplexBitboardOperations() {
    // Create a bitboard representing the center 4 squares
    let center = Square.d4.bb | Square.e4.bb | Square.d5.bb | Square.e5.bb
    
    // Test expansion in all directions
    let expanded = center.north() | center.south() | center.east() | center.west()
    
    // Should include the ring around the center
    XCTAssertTrue(expanded & Square.d3.bb != 0)  // south of d4
    XCTAssertTrue(expanded & Square.d6.bb != 0)  // north of d5
    XCTAssertTrue(expanded & Square.c4.bb != 0)  // west of d4
    XCTAssertTrue(expanded & Square.f4.bb != 0)  // east of e4
    
    // Should not include the original center squares
    XCTAssertEqual(expanded & center, 0)
  }
}