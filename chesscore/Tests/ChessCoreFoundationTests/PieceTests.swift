//
//  PieceTests.swift
//  ChessCoreFoundationTests
//

@testable import ChessCoreFoundation
import XCTest

final class PieceTests: XCTestCase {

  func testPieceKindNotation() {
    XCTAssertEqual(Piece.Kind.pawn.notation, "")
    XCTAssertEqual(Piece.Kind.bishop.notation, "B")
    XCTAssertEqual(Piece.Kind.knight.notation, "N")
    XCTAssertEqual(Piece.Kind.rook.notation, "R")
    XCTAssertEqual(Piece.Kind.queen.notation, "Q")
    XCTAssertEqual(Piece.Kind.king.notation, "K")
  }

  func testPieceColor() {
    let white = Piece.Color.white
    XCTAssertEqual(white.rawValue, "w")
    XCTAssertEqual(white.opposite, .black)

    let black = Piece.Color.black
    XCTAssertEqual(black.rawValue, "b")
    XCTAssertEqual(black.opposite, .white)
  }

  func testColorToggle() {
    var color = Piece.Color.white
    color.toggle()
    XCTAssertEqual(color, .black)
    
    color.toggle()
    XCTAssertEqual(color, .white)
  }

  func testPieceInitialization() {
    let piece = Piece(.queen, color: .white, square: .e4)
    XCTAssertEqual(piece.kind, .queen)
    XCTAssertEqual(piece.color, .white)
    XCTAssertEqual(piece.square, .e4)
  }

  func testFenRepresentation() {
    let sq = Square.a1

    // Test white pieces
    let wP = Piece(fen: "P", square: sq)
    XCTAssertEqual(wP?.color, .white)
    XCTAssertEqual(wP?.kind, .pawn)
    XCTAssertEqual(wP?.square, sq)

    let wB = Piece(fen: "B", square: sq)
    XCTAssertEqual(wB?.color, .white)
    XCTAssertEqual(wB?.kind, .bishop)

    let wN = Piece(fen: "N", square: sq)
    XCTAssertEqual(wN?.color, .white)
    XCTAssertEqual(wN?.kind, .knight)

    let wR = Piece(fen: "R", square: sq)
    XCTAssertEqual(wR?.color, .white)
    XCTAssertEqual(wR?.kind, .rook)

    let wQ = Piece(fen: "Q", square: sq)
    XCTAssertEqual(wQ?.color, .white)
    XCTAssertEqual(wQ?.kind, .queen)

    let wK = Piece(fen: "K", square: sq)
    XCTAssertEqual(wK?.color, .white)
    XCTAssertEqual(wK?.kind, .king)

    // Test black pieces
    let bP = Piece(fen: "p", square: sq)
    XCTAssertEqual(bP?.color, .black)
    XCTAssertEqual(bP?.kind, .pawn)

    let bB = Piece(fen: "b", square: sq)
    XCTAssertEqual(bB?.color, .black)
    XCTAssertEqual(bB?.kind, .bishop)

    let bN = Piece(fen: "n", square: sq)
    XCTAssertEqual(bN?.color, .black)
    XCTAssertEqual(bN?.kind, .knight)

    let bR = Piece(fen: "r", square: sq)
    XCTAssertEqual(bR?.color, .black)
    XCTAssertEqual(bR?.kind, .rook)

    let bQ = Piece(fen: "q", square: sq)
    XCTAssertEqual(bQ?.color, .black)
    XCTAssertEqual(bQ?.kind, .queen)

    let bK = Piece(fen: "k", square: sq)
    XCTAssertEqual(bK?.color, .black)
    XCTAssertEqual(bK?.kind, .king)
  }

  func testInvalidFenRepresentation() {
    XCTAssertNil(Piece(fen: "invalid", square: .a1))
    XCTAssertNil(Piece(fen: "", square: .a1))
    XCTAssertNil(Piece(fen: "X", square: .a1))
    XCTAssertNil(Piece(fen: "1", square: .a1))
  }

  func testFenOutput() {
    let whitePawn = Piece(.pawn, color: .white, square: .e2)
    XCTAssertEqual(whitePawn.fen, "P")
    
    let blackQueen = Piece(.queen, color: .black, square: .d8)
    XCTAssertEqual(blackQueen.fen, "q")
    
    let whiteKing = Piece(.king, color: .white, square: .e1)
    XCTAssertEqual(whiteKing.fen, "K")
    
    let blackKnight = Piece(.knight, color: .black, square: .b8)
    XCTAssertEqual(blackKnight.fen, "n")
  }

  func testGraphicRepresentation() {
    let sq = Square.a1

    // Test white pieces graphics
    let wP = Piece(.pawn, color: .white, square: sq)
    XCTAssertEqual(wP.graphic, "♙")
    
    let wB = Piece(.bishop, color: .white, square: sq)
    XCTAssertEqual(wB.graphic, "♗")
    
    let wN = Piece(.knight, color: .white, square: sq)
    XCTAssertEqual(wN.graphic, "♘")
    
    let wR = Piece(.rook, color: .white, square: sq)
    XCTAssertEqual(wR.graphic, "♖")
    
    let wQ = Piece(.queen, color: .white, square: sq)
    XCTAssertEqual(wQ.graphic, "♕")
    
    let wK = Piece(.king, color: .white, square: sq)
    XCTAssertEqual(wK.graphic, "♔")

    // Test black pieces graphics
    let bP = Piece(.pawn, color: .black, square: sq)
    XCTAssertEqual(bP.graphic, "♟\u{FE0E}")
    
    let bB = Piece(.bishop, color: .black, square: sq)
    XCTAssertEqual(bB.graphic, "♝")
    
    let bN = Piece(.knight, color: .black, square: sq)
    XCTAssertEqual(bN.graphic, "♞")
    
    let bR = Piece(.rook, color: .black, square: sq)
    XCTAssertEqual(bR.graphic, "♜")
    
    let bQ = Piece(.queen, color: .black, square: sq)
    XCTAssertEqual(bQ.graphic, "♛")
    
    let bK = Piece(.king, color: .black, square: sq)
    XCTAssertEqual(bK.graphic, "♚")
  }

  func testPieceDescription() {
    let piece = Piece(.queen, color: .white, square: .d1)
    let description = piece.description
    XCTAssertTrue(description.contains("White"))
    XCTAssertTrue(description.contains("Queen"))
    XCTAssertTrue(description.contains("d1"))
  }

  func testColorDescription() {
    XCTAssertEqual(Piece.Color.white.description, "White")
    XCTAssertEqual(Piece.Color.black.description, "Black")
  }

  func testKindDescription() {
    XCTAssertEqual(Piece.Kind.pawn.description, "Pawn")
    XCTAssertEqual(Piece.Kind.bishop.description, "Bishop")
    XCTAssertEqual(Piece.Kind.knight.description, "Knight")
    XCTAssertEqual(Piece.Kind.rook.description, "Rook")
    XCTAssertEqual(Piece.Kind.queen.description, "Queen")
    XCTAssertEqual(Piece.Kind.king.description, "King")
  }

  func testPieceEquality() {
    let piece1 = Piece(.king, color: .white, square: .e1)
    let piece2 = Piece(.king, color: .white, square: .e1)
    let piece3 = Piece(.king, color: .black, square: .e1)
    let piece4 = Piece(.queen, color: .white, square: .e1)
    let piece5 = Piece(.king, color: .white, square: .e8)
    
    XCTAssertEqual(piece1, piece2)
    XCTAssertNotEqual(piece1, piece3)  // Different color
    XCTAssertNotEqual(piece1, piece4)  // Different kind
    XCTAssertNotEqual(piece1, piece5)  // Different square
  }

  func testPieceHashability() {
    let piece1 = Piece(.king, color: .white, square: .e1)
    let piece2 = Piece(.king, color: .white, square: .e1)
    let piece3 = Piece(.king, color: .black, square: .e1)
    
    let set: Set<Piece> = [piece1, piece2, piece3]
    XCTAssertEqual(set.count, 2)  // piece1 and piece2 should be considered the same
  }

  func testAllPieceKinds() {
    let allKinds = Piece.Kind.allCases
    XCTAssertEqual(allKinds.count, 6)
    XCTAssertTrue(allKinds.contains(.pawn))
    XCTAssertTrue(allKinds.contains(.bishop))
    XCTAssertTrue(allKinds.contains(.knight))
    XCTAssertTrue(allKinds.contains(.rook))
    XCTAssertTrue(allKinds.contains(.queen))
    XCTAssertTrue(allKinds.contains(.king))
  }

  func testAllPieceColors() {
    let allColors = Piece.Color.allCases
    XCTAssertEqual(allColors.count, 2)
    XCTAssertTrue(allColors.contains(.white))
    XCTAssertTrue(allColors.contains(.black))
  }

  func testFenRoundtrip() {
    // Test that we can create a piece from FEN and get the same FEN back
    let testPieces = [
      ("P", Piece.Kind.pawn, Piece.Color.white),
      ("p", Piece.Kind.pawn, Piece.Color.black),
      ("R", Piece.Kind.rook, Piece.Color.white),
      ("r", Piece.Kind.rook, Piece.Color.black),
      ("N", Piece.Kind.knight, Piece.Color.white),
      ("n", Piece.Kind.knight, Piece.Color.black),
      ("B", Piece.Kind.bishop, Piece.Color.white),
      ("b", Piece.Kind.bishop, Piece.Color.black),
      ("Q", Piece.Kind.queen, Piece.Color.white),
      ("q", Piece.Kind.queen, Piece.Color.black),
      ("K", Piece.Kind.king, Piece.Color.white),
      ("k", Piece.Kind.king, Piece.Color.black)
    ]
    
    for (fenString, expectedKind, expectedColor) in testPieces {
      let piece = Piece(fen: fenString, square: .e4)
      XCTAssertNotNil(piece)
      XCTAssertEqual(piece?.kind, expectedKind)
      XCTAssertEqual(piece?.color, expectedColor)
      XCTAssertEqual(piece?.fen, fenString)
    }
  }

  func testGraphicConsistency() {
    // Test that graphic property returns appropriate Unicode symbols for all piece types
    let allPieceTypes: [(Piece.Kind, Piece.Color, String)] = [
      (.pawn, .white, "♙"),
      (.pawn, .black, "♟\u{FE0E}"),
      (.rook, .white, "♖"),
      (.rook, .black, "♜"),
      (.knight, .white, "♘"),
      (.knight, .black, "♞"),
      (.bishop, .white, "♗"),
      (.bishop, .black, "♝"),
      (.queen, .white, "♕"),
      (.queen, .black, "♛"),
      (.king, .white, "♔"),
      (.king, .black, "♚")
    ]
    
    for (kind, color, expectedGraphic) in allPieceTypes {
      let piece = Piece(kind, color: color, square: .e4)
      XCTAssertEqual(piece.graphic, expectedGraphic, "Graphic mismatch for \(color) \(kind)")
      
      // Verify graphic is different from FEN (they serve different purposes)
      XCTAssertNotEqual(piece.graphic, piece.fen, "Graphic should differ from FEN for \(color) \(kind)")
    }
  }

  func testPerformanceFenParsing() {
    measure {
      for _ in 0..<10000 {
        _ = Piece(fen: "Q", square: .e4)
      }
    }
  }

  func testPerformanceFenOutput() {
    let piece = Piece(.queen, color: .white, square: .e4)
    measure {
      for _ in 0..<10000 {
        _ = piece.fen
      }
    }
  }
}