//
//  PGNMultiGameParserTests.swift
//  ChessKitTests
//

@testable import ChessKit
import XCTest

final class PGNMultiGameParserTests: XCTestCase {

  // MARK: - Test Data
  
  private let twoGamePGN = """
    [Event "Test Tournament"]
    [Site "Test City"]
    [Date "2023.01.01"]
    [Round "1"]
    [White "Player A"]
    [Black "Player B"]
    [Result "1-0"]
    
    1. e4 e5 2. Nf3 Nc6 3. Bb5 a6 4. Ba4 Nf6 5. O-O Be7 6. Re1 b5 7. Bb3 d6 8. c3 O-O 
    9. h3 Nb8 10. d4 Nbd7 11. c4 c6 12. cxb5 axb5 13. Nc3 Bb7 14. Bg5 b4 15. Nb1 h6 
    16. Bh4 c5 17. dxe5 Nxe5 18. Nxe5 dxe5 19. f3 Bc5+ 20. Kh1 Qd4 21. Qe2 Rfd8 
    22. Nd2 g5 23. Bg3 Rd6 24. axb4 cxb4 25. b3 Rc8 26. Qf2 1-0

    [Event "Test Tournament"]
    [Site "Test City"]
    [Date "2023.01.01"]
    [Round "2"]
    [White "Player C"]
    [Black "Player D"]
    [Result "0-1"]
    
    1. d4 Nf6 2. c4 g6 3. Nc3 Bg7 4. e4 d6 5. Nf3 O-O 6. Be2 e5 7. O-O Nc6 8. d5 Ne7 
    9. Ne1 Nd7 10. Nd3 f5 11. Bd2 Nf6 12. f3 f4 13. c5 g5 14. Rc1 Ng6 15. c6 bxc6 
    16. dxc6 Nh5 17. Qc2 Bd7 18. Rfd1 Bh6 19. Bxf4 Nxf4 20. Nxf4 exf4 21. Bd3 g4 
    22. Ne2 gxf3 23. gxf3 Qh4 24. Rd2 Qh3 25. Rf2 Bg5 26. d6 Bh4 27. Rff1 cxd6 0-1
    """
  
  private let threeGamePGN = """
    [Event "World Championship"]
    [Site "London"]
    [Date "1851.07.01"]
    [Round "1"]
    [White "Anderssen"]
    [Black "Kieseritzky"]
    [Result "1-0"]
    
    1. e4 e5 2. f4 exf4 3. Bc4 Qh4+ 4. Kf1 b5 5. Bxb5 Nf6 6. Nf3 Qh6 7. d3 Nh5 
    8. Nh4 Qg5 9. Nf5 c6 10. g4 Nf6 11. Rg1 cxb5 12. h4 Qg6 13. h5 Qg5 14. Qf3 Ng8 
    15. Bxf4 Qf6 16. Nc3 Bc5 17. Nd5 Qxb2 18. Bd6 Bxg1 19. e5 Qxa1+ 20. Ke2 Na6 
    21. Nxg7+ Kd8 22. Qf6+ Nxf6 23. Be7# 1-0

    [Event "Casual Game"]
    [Site "Paris"]
    [Date "1858.??"]
    [Round "-"]
    [White "Morphy"]
    [Black "Duke and Count"]
    [Result "1-0"]
    
    1. e4 e5 2. Nf3 d6 3. d4 Bg4 {This is a comment} 4. dxe5 Bxf3 5. Qxf3 dxe5 
    6. Bc4 Nf6 7. Qb3 Qe7 8. Nc3 c6 9. Bg5 b5 10. Nxb5 cxb5 11. Bxb5+ Nbd7 
    12. O-O-O Rd8 13. Rxd7 Rxd7 14. Rd1 Qe6 15. Bxd7+ Nxd7 16. Qb8+ Nxb8 
    17. Rd8# 1-0

    [Event "Immortal Game"]
    [Site "London"]
    [Date "1851.06.21"]
    [Round "-"]
    [White "Anderssen"]
    [Black "Dufresne"]
    [Result "1-0"]
    
    1. e4 e5 2. Nf3 Nc6 3. Bc4 Bc5 4. b4 Bxb4 5. c3 Ba5 6. d4 exd4 7. O-O d3 
    8. Qb3 Qf6 9. e5 Qg6 10. Re1 Nge7 11. Ba3 b5 12. Qxb5 Rb8 13. Qa4 Bb6 
    14. Nbd2 Bb7 15. Ne4 Qf5 16. Bxd3 Qh5 17. Nf6+ gxf6 18. exf6 Rg8 
    19. Rad1 Qxf3 ! {A brilliant sacrifice} 20. Rxe7+ Nxe7 21. Qxd7+ Kxd7 
    22. Bf5+ Ke8 23. Bd7+ Kf8 24. Bxe7# 1-0
    """
  
  private let singleGamePGN = """
    [Event "Test Single Game"]
    [Site "Test Location"]
    [Date "2023.12.25"]
    [Round "1"]
    [White "White Player"]
    [Black "Black Player"]
    [Result "1/2-1/2"]
    
    1. e4 e5 2. Nf3 Nc6 3. Bb5 a6 4. Ba4 Nf6 5. O-O Be7 6. Re1 b5 7. Bb3 O-O 
    8. c3 d6 9. h3 Re8 10. d4 Bb7 11. Nbd2 Bf8 12. a4 h6 13. Bc2 exd4 
    14. cxd4 Nb4 15. Bb1 c5 16. d5 Nd7 17. Ra3 f5 18. exf5 Nf6 19. Rae3 Rxe3 
    20. Rxe3 Bd6 21. Re1 Qc7 22. g3 Re8 23. Rxe8+ Nxe8 24. Be3 Qc8 25. Qc2 Qd8 1/2-1/2
    """
  
  private let emptyGamesPGN = """
    
    
    
    """
  
  private let mixedFormatPGN = """
    [Event "Game 1"]
    [White "A"]
    [Black "B"]
    [Result "*"]
    
    1. e4 c5 *
    
    [Event "Game 2"]
    [White "C"]
    [Black "D"]
    [Result "1-0"]
    1. d4 d5 2. c4 1-0
    """

  // MARK: - Basic Functionality Tests
  
  func testParseMultipleGames_TwoGames() {
    let games = PGNParser.parseMultiple(games: twoGamePGN)
    
    XCTAssertEqual(games.count, 2, "Should parse exactly 2 games")
    
    // Test first game
    let firstGame = games[0]
    XCTAssertEqual(firstGame.tags.event, "Test Tournament")
    XCTAssertEqual(firstGame.tags.round, "1")
    XCTAssertEqual(firstGame.tags.white, "Player A")
    XCTAssertEqual(firstGame.tags.black, "Player B")
    XCTAssertEqual(firstGame.tags.result, "1-0")
    XCTAssertTrue(firstGame.positions.count > 20, "First game should have multiple positions")
    
    // Test second game
    let secondGame = games[1]
    XCTAssertEqual(secondGame.tags.event, "Test Tournament")
    XCTAssertEqual(secondGame.tags.round, "2")
    XCTAssertEqual(secondGame.tags.white, "Player C")
    XCTAssertEqual(secondGame.tags.black, "Player D")
    XCTAssertEqual(secondGame.tags.result, "0-1")
    XCTAssertTrue(secondGame.positions.count > 20, "Second game should have multiple positions")
  }
  
  func testParseMultipleGames_ThreeGames() {
    let games = PGNParser.parseMultiple(games: threeGamePGN)
    
    XCTAssertEqual(games.count, 3, "Should parse exactly 3 games")
    
    // Test first game (Immortal Game variant)
    let firstGame = games[0]
    XCTAssertEqual(firstGame.tags.white, "Anderssen")
    XCTAssertEqual(firstGame.tags.black, "Kieseritzky")
    XCTAssertEqual(firstGame.tags.result, "1-0")
    
    // Test second game (Opera House Game)
    let secondGame = games[1]
    XCTAssertEqual(secondGame.tags.white, "Morphy")
    XCTAssertEqual(secondGame.tags.black, "Duke and Count")
    XCTAssertEqual(secondGame.tags.result, "1-0")
    
    // Test third game (Immortal Game)
    let thirdGame = games[2]
    XCTAssertEqual(thirdGame.tags.white, "Anderssen")
    XCTAssertEqual(thirdGame.tags.black, "Dufresne")
    XCTAssertEqual(thirdGame.tags.result, "1-0")
  }
  
  func testParseMultipleGames_SingleGame() {
    let games = PGNParser.parseMultiple(games: singleGamePGN)
    
    XCTAssertEqual(games.count, 1, "Should parse exactly 1 game")
    
    let game = games[0]
    XCTAssertEqual(game.tags.event, "Test Single Game")
    XCTAssertEqual(game.tags.white, "White Player")
    XCTAssertEqual(game.tags.black, "Black Player")
    XCTAssertEqual(game.tags.result, "1/2-1/2")
  }

  // MARK: - Edge Case Tests
  
  func testParseMultipleGames_EmptyString() {
    let games = PGNParser.parseMultiple(games: "")
    XCTAssertEqual(games.count, 0, "Should return empty array for empty string")
  }
  
  func testParseMultipleGames_WhitespaceOnly() {
    let games = PGNParser.parseMultiple(games: emptyGamesPGN)
    XCTAssertEqual(games.count, 0, "Should return empty array for whitespace-only string")
  }
  
  func testParseMultipleGames_MixedFormats() {
    let games = PGNParser.parseMultiple(games: mixedFormatPGN)
    
    XCTAssertEqual(games.count, 2, "Should parse 2 games despite mixed formatting")
    
    let firstGame = games[0]
    XCTAssertEqual(firstGame.tags.event, "Game 1")
    XCTAssertEqual(firstGame.tags.result, "*")
    
    let secondGame = games[1]
    XCTAssertEqual(secondGame.tags.event, "Game 2")
    XCTAssertEqual(secondGame.tags.result, "1-0")
  }
  
  func testParseMultipleGames_InvalidPGN() {
    let invalidPGN = """
    [Event "Invalid Game"]
    [White "Player 1"]
    [Black "Player 2"]
    [Result "1-0"]
    
    1. e4 invalid_move 1-0
    
    [Event "Valid Game"]
    [White "Player 3"]
    [Black "Player 4"]
    [Result "0-1"]
    
    1. d4 d5 2. c4 c6 0-1
    """
    
    let games = PGNParser.parseMultiple(games: invalidPGN)
    
    // The parser currently accepts games with invalid moves but only parses valid moves
    // Both games should be parsed, but the invalid game will have fewer moves
    XCTAssertEqual(games.count, 2, "Should parse both games")
    XCTAssertEqual(games[0].tags.event, "Invalid Game")
    XCTAssertEqual(games[1].tags.event, "Valid Game")
    
    // Invalid game should have only parsed the valid "e4" move
    XCTAssertTrue(games[0].positions.count >= 1, "Invalid game should have at least starting position")
    XCTAssertTrue(games[1].positions.count > 2, "Valid game should have multiple positions")
  }

  // MARK: - Formatting Tests
  
  func testParseMultipleGames_WindowsLineEndings() {
    let windowsPGN = twoGamePGN.replacingOccurrences(of: "\n", with: "\r\n")
    let games = PGNParser.parseMultiple(games: windowsPGN)
    
    XCTAssertEqual(games.count, 2, "Should handle Windows line endings")
    XCTAssertEqual(games[0].tags.white, "Player A")
    XCTAssertEqual(games[1].tags.white, "Player C")
  }
  
  func testParseMultipleGames_MacLineEndings() {
    let macPGN = twoGamePGN.replacingOccurrences(of: "\n", with: "\r")
    let games = PGNParser.parseMultiple(games: macPGN)
    
    XCTAssertEqual(games.count, 2, "Should handle Mac line endings")
    XCTAssertEqual(games[0].tags.white, "Player A")
    XCTAssertEqual(games[1].tags.white, "Player C")
  }

  // MARK: - Result Format Tests
  
  func testParseMultipleGames_AllResultFormats() {
    let allResultsPGN = """
    [Event "White Win"]
    [White "A"]
    [Black "B"]
    [Result "1-0"]
    1. e4 e5 1-0
    
    [Event "Black Win"]
    [White "C"]
    [Black "D"]
    [Result "0-1"]
    1. d4 d5 0-1
    
    [Event "Draw"]
    [White "E"]
    [Black "F"]
    [Result "1/2-1/2"]
    1. c4 c5 1/2-1/2
    
    [Event "Ongoing"]
    [White "G"]
    [Black "H"]
    [Result "*"]
    1. Nf3 Nf6 *
    """
    
    let games = PGNParser.parseMultiple(games: allResultsPGN)
    
    XCTAssertEqual(games.count, 4, "Should parse all 4 games with different results")
    XCTAssertEqual(games[0].tags.result, "1-0")
    XCTAssertEqual(games[1].tags.result, "0-1")
    XCTAssertEqual(games[2].tags.result, "1/2-1/2")
    XCTAssertEqual(games[3].tags.result, "*")
  }

  // MARK: - Comment and Annotation Tests
  
  func testParseMultipleGames_WithComments() {
    let commentsPGN = """
    [Event "Game with Comments"]
    [White "A"]
    [Black "B"]
    [Result "1-0"]
    
    1. e4 e5 {Good opening} 2. Nf3 Nc6 {Developing} 3. Bb5 {Spanish Opening} a6 1-0
    
    [Event "Game with Time"]
    [White "C"]
    [Black "D"]
    [Result "0-1"]
    
    1. d4 {[%clk 0:30:00]} d5 {[%clk 0:29:58]} 2. c4 {[%clk 0:29:45]} c6 0-1
    """
    
    let games = PGNParser.parseMultiple(games: commentsPGN)
    
    XCTAssertEqual(games.count, 2, "Should parse games with comments")
    
    // Verify that comments are preserved (basic check)
    let firstGame = games[0]
    XCTAssertTrue(firstGame.positions.count > 3, "First game should have moves")
    
    let secondGame = games[1]
    XCTAssertTrue(secondGame.positions.count > 2, "Second game should have moves")
  }

  // MARK: - Performance Tests
  
  func testParseMultipleGames_LargeInput() {
    // Create a large PGN with many games
    var largePGN = ""
    for i in 1...100 {
      largePGN += """
      [Event "Game \(i)"]
      [White "Player \(i * 2 - 1)"]
      [Black "Player \(i * 2)"]
      [Result "1-0"]
      
      1. e4 e5 2. Nf3 Nc6 3. Bb5 1-0
      
      """
    }
    
    measure {
      let games = PGNParser.parseMultiple(games: largePGN)
      XCTAssertEqual(games.count, 100, "Should parse all 100 games")
    }
  }

  // MARK: - No Tags Edge Cases
  
  func testParseMultipleGames_NoTagsMultipleGames() {
    let noTagsPGN = """
    1. e4 e5 *
    1. d4 Nf6 *
    """
    
    let games = PGNParser.parseMultiple(games: noTagsPGN)
    
    XCTAssertEqual(games.count, 2, "Should parse exactly 2 games without tags")
    
    // First game should be "1. e4 e5 *"
    let firstGame = games[0]
    XCTAssertEqual(firstGame.moves.count, 2, "First game should have 2 moves")
    
    // Second game should be "1. d4 Nf6 *"
    let secondGame = games[1]
    XCTAssertEqual(secondGame.moves.count, 2, "Second game should have 2 moves")
  }

  // MARK: - Consistency Tests
  
  func testParseMultipleGames_ConsistencyWithSingleParser() {
    // Test that parsing a single game with parseMultiple gives same result as parse(game:)
    let singleGame = PGNParser.parse(game: singleGamePGN)
    let multipleGames = PGNParser.parseMultiple(games: singleGamePGN)
    
    XCTAssertNotNil(singleGame, "Single parser should succeed")
    XCTAssertEqual(multipleGames.count, 1, "Multiple parser should return 1 game")
    
    if let single = singleGame, !multipleGames.isEmpty {
      let multiple = multipleGames[0]
      
      // Compare basic properties
      XCTAssertEqual(single.tags.event, multiple.tags.event)
      XCTAssertEqual(single.tags.white, multiple.tags.white)
      XCTAssertEqual(single.tags.black, multiple.tags.black)
      XCTAssertEqual(single.tags.result, multiple.tags.result)
      XCTAssertEqual(single.positions.count, multiple.positions.count)
    }
  }
}