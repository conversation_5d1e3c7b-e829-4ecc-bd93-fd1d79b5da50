//
//  MoveTreeEditingTests.swift
//  ChessKitTests
//

@testable import ChessKit
import XCTest

final class MoveTreeEditingTests: XCTestCase {
    
    private var game: Game!
    
    override func setUp() {
        super.setUp()
        game = Game()
    }
    
    override func tearDown() {
        game = nil
        super.tearDown()
    }
    
    // MARK: - Add Move Tests
    
    func testAddSingleMove() {
        // Test adding a single move to empty game
        let moveIndex = game.make(move: "e4", from: MoveTree.minimumIndex)
        
        XCTAssertEqual(moveIndex, game.moves.nthIndex(0))
        XCTAssertFalse(game.moves.isEmpty)
        XCTAssertEqual(game.moves.count, 1)
        XCTAssertEqual(game.moves.allCount, 1)
        
        let move = game.moves.getNodeMove(index: moveIndex)
        XCTAssertEqual(move?.metaMove?.san, "e4")
        XCTAssertEqual(game.moves.getNodeColor(index: moveIndex), .white)
        XCTAssertEqual(game.moves.getNodeNumber(index: moveIndex), 1)
    }
    
    func testAddSequenceOfMoves() {
        // Test adding multiple moves in sequence
        let finalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: MoveTree.minimumIndex)
        
        XCTAssertEqual(game.moves.count, 4)
        XCTAssertEqual(game.moves.allCount, 4)
        
        // Get the history to access all indices
        let indices = game.moves.history(for: finalIndex)
        let sans = indices.compactMap { game.moves.getNodeMove(index: $0)?.metaMove?.san }
        XCTAssertEqual(sans, ["e4", "e5", "Nf3", "Nc6"])
        
        // Test colors and numbers
        XCTAssertEqual(game.moves.getNodeColor(index: indices[1]), .white)
        XCTAssertEqual(game.moves.getNodeNumber(index: indices[1]), 1)
        XCTAssertEqual(game.moves.getNodeColor(index: indices[2]), .black)
        XCTAssertEqual(game.moves.getNodeNumber(index: indices[2]), 1)
        XCTAssertEqual(game.moves.getNodeColor(index: indices[3]), .white)
        XCTAssertEqual(game.moves.getNodeNumber(index: indices[3]), 2)
        XCTAssertEqual(game.moves.getNodeColor(index: indices[4]), .black)
        XCTAssertEqual(game.moves.getNodeNumber(index: indices[4]), 2)
    }
    
    func testAddVariation() {
        // Set up main line: 1. e4 e5 2. Nf3
        let finalIndex = game.make(moves: ["e4", "e5", "Nf3"], from: MoveTree.minimumIndex)
        let mainIndices = game.moves.history(for: finalIndex)
        let e5Index = mainIndices[2]
        
        // Add variation after e5: 2. Nc3 instead of Nf3
        let variationIndex = game.make(move: "Nc3", from: e5Index)
        
        XCTAssertEqual(game.moves.allCount, 4) // 3 main + 1 variation
        XCTAssertFalse(game.moves.isOnMainVariation(index: variationIndex))
        
        let variationMove = game.moves.getNodeMove(index: variationIndex)
        XCTAssertEqual(variationMove?.metaMove?.san, "Nc3")
        XCTAssertEqual(game.moves.getNodeColor(index: variationIndex), .white)
        XCTAssertEqual(game.moves.getNodeNumber(index: variationIndex), 2)
    }
    
    func testAddMultipleVariations() {
        // Set up main line: 1. e4 e5
        let finalIndex = game.make(moves: ["e4", "e5"], from: MoveTree.minimumIndex)
        let mainIndices = game.moves.history(for: finalIndex)
        let e5Index = mainIndices[2]    // headNode, e4, e5
        
        // Add multiple variations after e5
        let _ = game.make(move: "Nf3", from: e5Index)
        let variation2 = game.make(move: "Nc3", from: e5Index)
        let variation3 = game.make(move: "Bc4", from: e5Index)
        
        XCTAssertEqual(game.moves.allCount, 5) // 2 main + 3 variations
        
        let variations = game.moves.variations(from: e5Index)
        XCTAssertEqual(variations.count, 2) // Nc3 and Bc4 (Nf3 is main continuation)
        XCTAssertTrue(variations.contains(variation2))
        XCTAssertTrue(variations.contains(variation3))
    }
    
    // MARK: - Delete Move Tests
    
    func testDeleteSingleMove() {
        // Set up: 1. e4 e5
        let finalIndex = game.make(moves: ["e4", "e5"], from: MoveTree.minimumIndex)
        let indices = game.moves.history(for: finalIndex)
        let e5Index = indices[2]
        
        // Delete e5
        let success = game.delete(at: e5Index)
        
        XCTAssertTrue(success)
        XCTAssertEqual(game.moves.count, 1) // Only e4 remains
        XCTAssertEqual(game.moves.allCount, 1) // e4
        XCTAssertNil(game.moves.getNodeMove(index: e5Index))
    }
    
    func testDeleteMoveWithContinuation() {
        // Set up: 1. e4 e5 2. Nf3 Nc6
        let finalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: MoveTree.minimumIndex)
        let indices = game.moves.history(for: finalIndex)
        let e5Index = indices[2]
        
        // Delete e5 (should also delete Nf3 and Nc6)
        let success = game.delete(at: e5Index)
        
        XCTAssertTrue(success)
        XCTAssertEqual(game.moves.count, 1) // Only e4 remains
        XCTAssertEqual(game.moves.allCount, 1) // e4
        
        // All subsequent moves should be deleted
        XCTAssertNil(game.moves.getNodeMove(index: indices[2]))
        XCTAssertNil(game.moves.getNodeMove(index: indices[3]))
        XCTAssertNil(game.moves.getNodeMove(index: indices[4]))
    }
    
    func testDeleteVariation() {
        // Set up main line: 1. e4 e5 2. Nf3 Nc6
        let mainFinalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: MoveTree.minimumIndex)
        let mainIndices = game.moves.history(for: mainFinalIndex)
        let e5Index = mainIndices[2]
        
        // Add variation: 2. Nc3 Nf6
        let variationFinalIndex = game.make(moves: ["Nc3", "Nf6"], from: e5Index)
        let variationIndices = game.moves.history(for: variationFinalIndex).suffix(2) // Last 2 moves
        
        // Delete the variation starting with Nc3
        let success = game.delete(at: Array(variationIndices)[0])
        
        XCTAssertTrue(success)
        XCTAssertEqual(game.moves.allCount, 4) // 4 main moves
        
        // Main line should remain intact
        let mainSans = mainIndices.compactMap { game.moves.getNodeMove(index: $0)?.metaMove?.san }
        XCTAssertEqual(mainSans, ["e4", "e5", "Nf3", "Nc6"])
        
        // Variation should be deleted
        XCTAssertNil(game.moves.getNodeMove(index: Array(variationIndices)[0]))
        XCTAssertNil(game.moves.getNodeMove(index: Array(variationIndices)[1]))
    }
    
    func testDeleteNonExistentMove() {
        // Try to delete a move that doesn't exist
        let success = game.delete(at: 999)
        XCTAssertFalse(success)
    }
    
    // MARK: - Promote Move Tests
    
    func testPromoteVariationToMain() {
        // Set up: 1. e4 e5 2. Nf3 (2. Nc3) Nc6
        let mainFinalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: MoveTree.minimumIndex)
        let mainIndices = game.moves.history(for: mainFinalIndex)
        let e5Index = mainIndices[2]
        let variationIndex = game.make(move: "Nc3", from: e5Index)
        
        // Promote Nc3 variation
        let success = game.promote(index: variationIndex)
        
        XCTAssertTrue(success)
        
        // Now Nc3 should be the main continuation after e5
        let nextIndex = game.moves.nextIndex(currentIndex: e5Index)
        let nextMove = game.moves.getNodeMove(index: nextIndex!)
        XCTAssertEqual(nextMove?.metaMove?.san, "Nc3")
        
        // Nf3 should now be a variation
        let variations = game.moves.variations(from: e5Index)
        XCTAssertEqual(variations.count, 1)
        let variationMove = game.moves.getNodeMove(index: variations[0])
        XCTAssertEqual(variationMove?.metaMove?.san, "Nf3")
    }
    
    func testPromoteToMainVariation() {
        // Set up: 1. e4 e5 2. Nf3 Nc6 3. Bc4 (3. Bb5) Be7
        let mainFinalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6", "Bc4", "Be7"], from: MoveTree.minimumIndex)
        let mainIndices = game.moves.history(for: mainFinalIndex)
        let nc6Index = mainIndices[4]
        let variationFinalIndex = game.make(moves: ["Bb5", "a6"], from: nc6Index)
        let variationIndices = game.moves.history(for: variationFinalIndex).suffix(2) // Last 2 moves
        
        // Promote the entire Bb5 variation to main
        let success = game.promoteToMainVariation(index: Array(variationIndices)[1])
        
        XCTAssertTrue(success)
        XCTAssertTrue(game.moves.isOnMainVariation(index: Array(variationIndices)[0]))
        XCTAssertTrue(game.moves.isOnMainVariation(index: Array(variationIndices)[1]))
    }
    
    func testPromoteAlreadyMainVariation() {
        // Set up main line
        let mainFinalIndex = game.make(moves: ["e4", "e5"], from: MoveTree.minimumIndex)
        let mainIndices = game.moves.history(for: mainFinalIndex)
        
        // Try to promote a move that's already on the main variation
        let success = game.promote(index: mainIndices[1])
        XCTAssertFalse(success)
    }
    
    func testPromoteNonExistentMove() {
        // Try to promote a move that doesn't exist
        let success = game.promote(index: 999)
        XCTAssertFalse(success)
    }
    
    // MARK: - Complex Editing Scenarios
    
    func testComplexVariationStructure() {
        // Build a complex tree structure
        // 1. e4 e5 2. Nf3 (2. Nc3 Nf6 (2... Nc6)) Nc6 3. Bc4
        let mainFinalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6", "Bc4"], from: MoveTree.minimumIndex)
        let mainIndices = game.moves.history(for: mainFinalIndex)
        let e5Index = mainIndices[2]
        
        // Add first variation: 2. Nc3 Nf6
        let var1FinalIndex = game.make(moves: ["Nc3", "Nf6"], from: e5Index)
        let var1Indices = game.moves.history(for: var1FinalIndex).suffix(2) // Last 2 moves
        
        // Add sub-variation to Nc3: 2... Nc6 instead of Nf6
        let subVarIndex = game.make(move: "Nc6", from: Array(var1Indices)[0])
        
        XCTAssertEqual(game.moves.allCount, 8) // 5 main + 3 variations
        
        // Test structure
        XCTAssertTrue(game.moves.isOnMainVariation(index: mainIndices[2])) // Nf3 is main
        XCTAssertFalse(game.moves.isOnMainVariation(index: Array(var1Indices)[0])) // Nc3 is variation
        XCTAssertFalse(game.moves.isOnMainVariation(index: subVarIndex)) // Sub-variation
        
        let variations = game.moves.variations(from: Array(var1Indices)[0])
        XCTAssertEqual(variations.count, 1) // One sub-variation
    }
    
    func testEditAfterDeletion() {
        // Set up and then edit after deletion
        let finalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: MoveTree.minimumIndex)
        let indices = game.moves.history(for: finalIndex)
        
        // Delete the last two moves
        let success = game.delete(at: indices[3])
        XCTAssertTrue(success)
        
        // Add different moves
        let newFinalIndex = game.make(moves: ["d3", "d6"], from: indices[2])
        let newIndices = game.moves.history(for: newFinalIndex).suffix(2) // Last 2 moves
        
        XCTAssertEqual(game.moves.count, 4) // e4, e5, d3, d6
        let newSans = [indices[1], indices[2], Array(newIndices)[0], Array(newIndices)[1]]
            .compactMap { game.moves.getNodeMove(index: $0)?.metaMove?.san }
        XCTAssertEqual(newSans, ["e4", "e5", "d3", "d6"])
    }
    
    func testMultiplePromotions() {
        // Test multiple promotions in sequence
        // 1. e4 e5 2. Nf3 (2. Nc3 (2. d3)) Nc6
        let mainFinalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: MoveTree.minimumIndex)
        let mainIndices = game.moves.history(for: mainFinalIndex)
        let e5Index = mainIndices[2]
        
        let var1Index = game.make(move: "Nc3", from: e5Index)
        let var2Index = game.make(move: "d3", from: e5Index)
        
        // Promote d3 first
        let success1 = game.promote(index: var2Index)
        XCTAssertTrue(success1)
        
        // Now promote Nc3
        let success2 = game.promote(index: var1Index)
        XCTAssertTrue(success2)
        
        // Nc3 should now be main, d3 first variation, Nf3 second variation
        let nextMove = game.moves.getNodeMove(index: game.moves.nextIndex(currentIndex: e5Index)!)
        XCTAssertEqual(nextMove?.metaMove?.san, "Nc3")
        
        let variations = game.moves.variations(from: e5Index)
        XCTAssertEqual(variations.count, 2)
    }
    
    func testHistoryAndFuture() {
        // Test history and future functions after editing
        let finalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6", "Bc4"], from: MoveTree.minimumIndex)
        let indices = game.moves.history(for: finalIndex)
        let lastIndex = finalIndex
        
        let history = game.moves.history(for: lastIndex)
        XCTAssertEqual(history.count, 6)
        XCTAssertEqual(history, indices)
        
        let future = game.moves.future(for: indices[2])
        XCTAssertEqual(future, Array(indices[3...]))
        
        let fullVariation = game.moves.fullVariation(for: indices[2])
        XCTAssertEqual(fullVariation, indices)
    }
    
    func testPathCalculation() {
        // Test path calculation between different nodes
        let finalIndex = game.make(moves: ["e4", "e5", "Nf3"], from: MoveTree.minimumIndex)
        let indices = game.moves.history(for: finalIndex)
        let e5Index = indices[2]
        let variationIndex = game.make(move: "Nc3", from: e5Index)
        // 0. Path from a node to itself (should be empty)
        let selfPath = game.moves.path(from: indices[3], to: indices[3])
        XCTAssertEqual(selfPath.count, 0)
        // 1. Path from main line to variation
        let path = game.moves.path(from: indices[3], to: variationIndex)
        XCTAssertEqual(path.count, 2)
        XCTAssertEqual(path[0].direction, .reverse)
        XCTAssertEqual(path[1].direction, .forward)
        // 2. Path from variation back to main line
        let reversePath = game.moves.path(from: variationIndex, to: indices[3])
        XCTAssertEqual(reversePath.count, 2)
        XCTAssertEqual(reversePath[0].direction, .reverse)
        XCTAssertEqual(reversePath[1].direction, .forward)
        // 3. Path between sibling variations
        let var2Index = game.make(move: "d3", from: e5Index)
        let siblingPath = game.moves.path(from: variationIndex, to: var2Index)
        XCTAssertEqual(siblingPath.count, 2)
        XCTAssertEqual(siblingPath[0].direction, .reverse)
        XCTAssertEqual(siblingPath[1].direction, .forward)
        // 4. Path between deeper variations
        let var2Child = game.make(move: "Nc6", from: var2Index)
        let deepPath = game.moves.path(from: variationIndex, to: var2Child)
        XCTAssertEqual(deepPath.count, 3)
        XCTAssertEqual(deepPath[0].direction, .reverse)
        XCTAssertEqual(deepPath[1].direction, .forward)
        XCTAssertEqual(deepPath[2].direction, .forward)
    }
    
    // MARK: - Delete All After Tests
    
    func testDeleteAllAfterFromMiddleOfMainLine() {
        // Set up: 1. e4 e5 2. Nf3 Nc6 3. Bc4 Be7
        let finalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6", "Bc4", "Be7"], from: MoveTree.minimumIndex)
        let indices = game.moves.history(for: finalIndex)
        let e5Index = indices[2]
        
        // Delete all after e5 (should remove Nf3, Nc6, Bc4, Be7)
        let success = game.deleteAllAfter(index: e5Index)
        
        XCTAssertTrue(success)
        XCTAssertEqual(game.moves.count, 2) // Only e4 and e5 remain
        XCTAssertEqual(game.moves.allCount, 2) // e4 + e5
        
        // Verify remaining moves
        XCTAssertNotNil(game.moves.getNodeMove(index: indices[1])) // e4
        XCTAssertNotNil(game.moves.getNodeMove(index: indices[2])) // e5
        XCTAssertNil(game.moves.getNodeMove(index: indices[3])) // Nf3 deleted
        XCTAssertNil(game.moves.getNodeMove(index: indices[4])) // Nc6 deleted
        XCTAssertNil(game.moves.getNodeMove(index: indices[5])) // Bc4 deleted
        XCTAssertNil(game.moves.getNodeMove(index: indices[6])) // Be7 deleted
    }
    
    func testDeleteAllAfterFromMoveWithVariations() {
        // Set up: 1. e4 e5 2. Nf3 (2. Nc3 Nf6) (2. d3 d6) Nc6
        let mainFinalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: MoveTree.minimumIndex)
        let mainIndices = game.moves.history(for: mainFinalIndex)
        let e5Index = mainIndices[2]
        
        // Add variations
        let _ = game.make(moves: ["Nc3", "Nf6"], from: e5Index)
        let _ = game.make(moves: ["d3", "d6"], from: e5Index)
        
        // Delete all after e5 (should remove main line and all variations)
        let success = game.deleteAllAfter(index: e5Index)
        
        XCTAssertTrue(success)
        XCTAssertEqual(game.moves.count, 2) // Only e4 and e5 remain
        XCTAssertEqual(game.moves.allCount, 2) // e4 + e5
        
        // Verify no next move or variations exist after e5
        XCTAssertNil(game.moves.nextIndex(currentIndex: e5Index))
        XCTAssertEqual(game.moves.variations(from: e5Index).count, 0)
    }
    
    func testDeleteAllAfterFromVariation() {
        // Set up: 1. e4 e5 2. Nf3 Nc6 3. Bc4 (3. Bb5 a6 4. Ba4)
        let mainFinalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6", "Bc4"], from: MoveTree.minimumIndex)
        let mainIndices = game.moves.history(for: mainFinalIndex)
        let nc6Index = mainIndices[4]
        
        // Add variation: 3. Bb5 a6 4. Ba4
        let variationFinalIndex = game.make(moves: ["Bb5", "a6", "Ba4"], from: nc6Index)
        let variationIndices = game.moves.history(for: variationFinalIndex).suffix(3)
        let bb5Index = Array(variationIndices)[0]
        let a6Index = Array(variationIndices)[1]
        
        // Delete all after Bb5 (should remove a6 and Ba4)
        let success = game.deleteAllAfter(index: bb5Index)
        
        XCTAssertTrue(success)
        
        // Main line should remain intact
        XCTAssertNotNil(game.moves.getNodeMove(index: mainIndices[1])) // e4
        XCTAssertNotNil(game.moves.getNodeMove(index: mainIndices[2])) // e5
        XCTAssertNotNil(game.moves.getNodeMove(index: mainIndices[3])) // Nf3
        XCTAssertNotNil(game.moves.getNodeMove(index: mainIndices[4])) // Nc6
        XCTAssertNotNil(game.moves.getNodeMove(index: mainIndices[5])) // Bc4
        
        // Bb5 should remain but a6 and Ba4 should be deleted
        XCTAssertNotNil(game.moves.getNodeMove(index: bb5Index)) // Bb5
        XCTAssertNil(game.moves.getNodeMove(index: a6Index)) // a6 deleted
        XCTAssertNil(game.moves.getNodeMove(index: Array(variationIndices)[2])) // Ba4 deleted
    }
    
    func testDeleteAllAfterFromNodeWithNoNext() {
        // Set up: 1. e4 e5 2. Nf3
        let finalIndex = game.make(moves: ["e4", "e5", "Nf3"], from: MoveTree.minimumIndex)
        let indices = game.moves.history(for: finalIndex)
        let nf3Index = indices[3]
        
        // Try to delete all after Nf3 (which has no next move)
        let success = game.deleteAllAfter(index: nf3Index)
        
        XCTAssertFalse(success) // Should return false as there's nothing to delete
        XCTAssertEqual(game.moves.count, 3) // All moves should remain
        XCTAssertEqual(game.moves.allCount, 3) // 3 moves
    }
    
    func testDeleteAllAfterFromNodeWithVariationsOnly() {
        // Set up: 1. e4 e5 2. Nf3 Nc6 with variation 3. Bc4 (3. Bb5)
        let mainFinalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: MoveTree.minimumIndex)
        let mainIndices = game.moves.history(for: mainFinalIndex)
        let nc6Index = mainIndices[4]
        
        // Add only variations, no main continuation
        let _ = game.make(move: "Bc4", from: nc6Index)
        let _ = game.make(move: "Bb5", from: nc6Index)
        
        // Delete all after Nc6 (should remove all variations)
        let success = game.deleteAllAfter(index: nc6Index)
        
        XCTAssertTrue(success)
        XCTAssertEqual(game.moves.count, 4) // Only main line to Nc6 remains
        XCTAssertEqual(game.moves.allCount, 4) // 4 moves
        
        // Verify no variations exist after Nc6
        XCTAssertEqual(game.moves.variations(from: nc6Index).count, 0)
    }
    
    func testDeleteAllAfterWithComplexVariationTree() {
        // Set up complex tree: 1. e4 e5 2. Nf3 (2. Nc3 Nf6 (2... Nc6 3. d3)) Nc6
        let mainFinalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: MoveTree.minimumIndex)
        let mainIndices = game.moves.history(for: mainFinalIndex)
        let e5Index = mainIndices[2]
        
        // Add first variation: 2. Nc3 Nf6
        let var1FinalIndex = game.make(moves: ["Nc3", "Nf6"], from: e5Index)
        let var1Indices = game.moves.history(for: var1FinalIndex).suffix(2)
        let nc3Index = Array(var1Indices)[0]
        
        // Add sub-variation: 2... Nc6 3. d3
        let _ = game.make(moves: ["Nc6", "d3"], from: nc3Index)
        
        // Delete all after e5 (should remove entire tree)
        let success = game.deleteAllAfter(index: e5Index)
        
        XCTAssertTrue(success)
        XCTAssertEqual(game.moves.count, 2) // Only e4 and e5 remain
        XCTAssertEqual(game.moves.allCount, 2) // 2 moves
        
        // Verify no next move or variations exist after e5
        XCTAssertNil(game.moves.nextIndex(currentIndex: e5Index))
        XCTAssertEqual(game.moves.variations(from: e5Index).count, 0)
    }
    
    func testDeleteAllAfterNonExistentNode() {
        // Try to delete after a non-existent node
        let success = game.deleteAllAfter(index: 9999)
        
        XCTAssertFalse(success)
        XCTAssertEqual(game.moves.count, 0) // Empty game should remain empty
    }
    
    func testDeleteAllAfterUpdatesPositions() {
        // Set up: 1. e4 e5 2. Nf3 Nc6
        let finalIndex = game.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: MoveTree.minimumIndex)
        let indices = game.moves.history(for: finalIndex)
        let e5Index = indices[2]
        
        // Verify positions exist before deletion
        XCTAssertNotNil(game.positions[indices[3]]) // Position after Nf3
        XCTAssertNotNil(game.positions[indices[4]]) // Position after Nc6
        
        // Delete all after e5
        let success = game.deleteAllAfter(index: e5Index)
        
        XCTAssertTrue(success)
        
        // Verify positions are removed
        XCTAssertNil(game.positions[indices[3]]) // Position after Nf3 deleted
        XCTAssertNil(game.positions[indices[4]]) // Position after Nc6 deleted
        
        // Verify positions before deletion point remain
        XCTAssertNotNil(game.positions[indices[1]]) // Position after e4
        XCTAssertNotNil(game.positions[indices[2]]) // Position after e5
    }
}
