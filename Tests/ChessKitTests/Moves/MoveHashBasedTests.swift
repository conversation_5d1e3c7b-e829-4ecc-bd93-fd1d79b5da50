//
//  MoveHashBasedTests.swift
//  ChessKitTests
//

import XCTest
@testable import ChessKit

final class MoveHashBasedTests: XCTestCase {

 func testBasicMoveHashOperations() {
   var tree = MoveTree()
   
   // Test basic tree construction with index methods
   let startPosition = Position.standard
   let afterE4 = Position(fen: "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1")!
   let afterE5 = Position(fen: "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e6 0 2")!
   
   let e4 = Move(metaMove: MetaMove(san: "e4", position: startPosition))
   let e4Index = tree.add(move: e4, toParentIndex: MoveTree.minimumIndex)
   
   let e5 = Move(metaMove: MetaMove(san: "e5", position: afterE4))
   let e5Index = tree.add(move: e5, toParentIndex: e4Index)
   
   let nf3 = Move(metaMove: MetaMove(san: "Nf3", position: afterE5))
   let nf3Index = tree.add(move: nf3, toParentIndex: e5Index)
   
   // Add variations using index
   let nc3 = Move(metaMove: MetaMove(san: "Nc3", position: afterE5))
   let nc3Index = tree.add(move: nc3, toParentIndex: e5Index)
   
   XCTAssertEqual(tree.indices.count, 5)  // headNode, e4, e5, nf3, nc3
   XCTAssertNotNil(tree.dictionary[e4Index])
   XCTAssertNotNil(tree.dictionary[e5Index])
   XCTAssertNotNil(tree.dictionary[nf3Index])
   XCTAssertNotNil(tree.dictionary[nc3Index])
   
   // Test index-based methods
   XCTAssertTrue(tree.isOnMainVariation(index: e4Index))
   XCTAssertTrue(tree.isOnMainVariation(index: e5Index))
   XCTAssertTrue(tree.isOnMainVariation(index: nf3Index))
   XCTAssertFalse(tree.isOnMainVariation(index: nc3Index))
   
   // Test promotion using index
   let promoteSuccess = tree.promote(index: nc3Index) // promote nc3 to main variation
   XCTAssertTrue(promoteSuccess)

   // headNode, e4, e5, nc3, nf3
   
   // After promotion, nc3 should be on main variation
   XCTAssertTrue(tree.isOnMainVariation(index: nc3Index))
   XCTAssertFalse(tree.isOnMainVariation(index: nf3Index))
   
   // Test deletion using index
   let deleteSuccess = tree.delete(at: nc3Index)
   XCTAssertTrue(deleteSuccess)

   // headNode, e4, e5, nf3
   
   // After deletion, nc3 should not exist
   XCTAssertNil(tree.dictionary[nc3Index])
   XCTAssertEqual(tree.indices.count, 4)
   
   // Test history and future using index
   let history = tree.history(for: e5Index)
   XCTAssertEqual(history.count, 3) // headNode, e4, e5
   
   let future = tree.future(for: e4Index)
   XCTAssertEqual(future.count, 2) // e5, nf3
   
   let fullVariation = tree.fullVariation(for: e5Index)
   XCTAssertEqual(fullVariation.count, 4) // headNode, e4, e5, nf3
 }
 
 func testPromoteToMainVariation() {
   var tree = MoveTree()
   
   let startPosition = Position.standard
   let afterE4 = Position(fen: "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1")!
   let afterE5 = Position(fen: "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e6 0 2")!
   
   let e4 = Move(metaMove: MetaMove(san: "e4", position: startPosition))
   let e4Index = tree.add(move: e4, toParentIndex: MoveTree.minimumIndex)
   
   let e5 = Move(metaMove: MetaMove(san: "e5", position: afterE4))
   let e5Index = tree.add(move: e5, toParentIndex: e4Index)
   
   let nf3 = Move(metaMove: MetaMove(san: "Nf3", position: afterE5))
   let nf3Index = tree.add(move: nf3, toParentIndex: e5Index)
   
   let d4 = Move(metaMove: MetaMove(san: "d4", position: afterE5))
   let d4Index = tree.add(move: d4, toParentIndex: e5Index)
   
   // Initially, nf3 should be on main variation, d4 should not
   XCTAssertTrue(tree.isOnMainVariation(index: nf3Index))
   XCTAssertFalse(tree.isOnMainVariation(index: d4Index))
   
   // Promote d4 to main variation
   let success = tree.promoteToMainVariation(index: d4Index)
   XCTAssertTrue(success)
   
   // After promotion, d4 should be on main variation
   XCTAssertTrue(tree.isOnMainVariation(index: d4Index))
   XCTAssertFalse(tree.isOnMainVariation(index: nf3Index))
 }
 
 func testVariationsFromIndex() {
   var tree = MoveTree()
   
   let startPosition = Position.standard
   let afterE4 = Position(fen: "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1")!
   let afterE5 = Position(fen: "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e6 0 2")!
   
   let e4 = Move(metaMove: MetaMove(san: "e4", position: startPosition))
   let e4Index = tree.add(move: e4, toParentIndex: MoveTree.minimumIndex)
   
   let e5 = Move(metaMove: MetaMove(san: "e5", position: afterE4))
   let e5Index = tree.add(move: e5, toParentIndex: e4Index)
   
   let nf3 = Move(metaMove: MetaMove(san: "Nf3", position: afterE5))
   let _ = tree.add(move: nf3, toParentIndex: e5Index)
   
   let d4 = Move(metaMove: MetaMove(san: "d4", position: afterE5))
   let _ = tree.add(move: d4, toParentIndex: e5Index)
   
   let nc3 = Move(metaMove: MetaMove(san: "Nc3", position: afterE5))
   let _ = tree.add(move: nc3, toParentIndex: e5Index)
   
   // Test variations from e5
   let variations = tree.variations(from: e5Index)
   XCTAssertEqual(variations.count, 2) // d4 and nc3 should be variations
   
   // Test that we can get moves from the variations
   let variationMoves = variations.compactMap { tree.getNodeMove(index: $0)?.metaMove?.san }
   XCTAssertTrue(variationMoves.contains("d4"))
   XCTAssertTrue(variationMoves.contains("Nc3"))
 }
}
