//
//  GameTests.swift
//  ChessKitTests
//

@testable import ChessKit
import XCTest

final class GameTests: XCTestCase {

   private var game: Game!

   override func setUp() {
       super.setUp()
       game = Game()
       game.tags = Self.mockTags
       
       // 1. e4 e5 2. Nf3 Nc6 3. Bc4
       game.make(moves: ["e4", "e5", "Nf3", "Nc6", "Bc4"], from: MoveTree.minimumIndex)
       
       let e5Index = game.moves.history(for: game.moves.lastMainVariationIndex).first {
           game.moves.getNodeMove(index: $0)?.metaMove?.san == "e5"
       }!
       
       // add variation to 2. Nf3: 2. Nc3 Nf6
       game.make(moves: ["Nc3", "Nf6"], from: e5Index)
   }

   override func tearDown() {
       game = nil
       super.tearDown()
   }
   
   func testStartingPosition() {
       let game1 = Game(startingWith: .standard)
       XCTAssertEqual(game1.startingPosition, .standard)

       let fen = "r1bqkb1r/pp1ppppp/2n2n2/8/2B1P3/2N2N2/PP3PPP/R1BQK2R b KQkq - 4 6"
       var game2 = Game(startingWith: .init(fen: fen)!)

       XCTAssertEqual(game2.startingPosition, .init(fen: fen)!)
       game2.make(move: "O-O", from: game2.startingIndex)
       
       let move = game2.moves.getNodeMove(index: game2.moves.firstIndex!)
       XCTAssertEqual(move?.metaMove?.san, "O-O")
   }

   func testMakeMoves() {
       XCTAssertFalse(game.moves.isEmpty)
       
       let mainLine = game.moves.fullVariation(for: game.moves.lastMainVariationIndex)
       let sans = mainLine.compactMap { game.moves.getNodeMove(index: $0)?.metaMove?.san }
       
       XCTAssertEqual(sans, ["e4", "e5", "Nf3", "Nc6", "Bc4"])
   }

   func testMoveAnnotation() {
       let nf3Index = game.moves.history(for: game.moves.lastMainVariationIndex).first {
           game.moves.getNodeMove(index: $0)?.metaMove?.san == "Nf3"
       }!
       
       if var move = game.moves.getNodeMove(index: nf3Index) {
           move.metaMove?.moveAssessment = .brilliant
           move.metaMove?.positionAssessment = .whiteDecisiveAdv
           move.positionComment.text = "A brilliant move!"
           game.moves.setNodeMove(index: nf3Index, newMove: move)
       }

       let move = game.moves.getNodeMove(index: nf3Index)
       XCTAssertEqual(move?.metaMove?.moveAssessment, .brilliant)
       XCTAssertEqual(move?.metaMove?.positionAssessment, .whiteDecisiveAdv)
       XCTAssertEqual(move?.positionComment.text, "A brilliant move!")
   }

   func testPGNGeneration() {
       let pgn =
         """
         [Event "Test Event"]
         [Site "Barrow, Alaska USA"]
         [Date "2000.01.01"]
         [Round "5"]
         [White "Player One"]
         [Black "Player Two"]
         [Result "1-0"]
         [Annotator "Annotator"]
         [PlyCount "15"]
         [TimeControl "40/7200:3600"]
         [Time "12:00"]
         [Termination "abandoned"]
         [Mode "OTB"]
         [FEN "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"]
         [SetUp "1"]
         [TestKey1 "Test Value 1"]
         [TestKey2 "Test Value 2"]

         1. e4 e5 2. Nf3 (2. Nc3 Nf6) 2... Nc6 3. Bc4 1-0
         """
       
       XCTAssertEqual(game.pgn, pgn)
   }

   func testPGNParsing() {
       let pgn =
         """
         [Event "Test Event"]
         [Site "Barrow, Alaska USA"]
         [Date "2000.01.01"]
         [Round "5"]
         [White "Player One"]
         [Black "Player Two"]
         [Result "1-0"]

         1. e4 e5 {A classic opening.} 2. Nf3 Nc6 (2... d6 3. d4) 3. Bb5 a6
         """
       
       let parsedGame = Game(pgn: pgn)!
       XCTAssertEqual(parsedGame.tags.event, "Test Event")
       XCTAssertEqual(parsedGame.tags.result, "1-0")
       
       let e5Index = parsedGame.moves.history(for: parsedGame.moves.lastMainVariationIndex).first {
           parsedGame.moves.getNodeMove(index: $0)?.metaMove?.san == "e5"
       }!
       
       let e5Move = parsedGame.moves.getNodeMove(index: e5Index)
       XCTAssertEqual(e5Move?.positionComment.text, "A classic opening.")
       
       let d6Index = parsedGame.moves.indices.first {
           parsedGame.moves.getNodeMove(index: $0)?.metaMove?.san == "d6"
       }!
       
       XCTAssertFalse(parsedGame.moves.isOnMainVariation(index: d6Index))
   }
   
   // MARK: - Delete All After Tests
   
   func testDeleteAllAfterIntegration() {
       // Test deleteAllAfter at the Game level
       var testGame = Game()
       let finalIndex = testGame.make(moves: ["e4", "e5", "Nf3", "Nc6", "Bc4"], from: MoveTree.minimumIndex)
       let indices = testGame.moves.history(for: finalIndex)
       let e5Index = indices[2]
       
       // Delete all after e5
       let success = testGame.deleteAllAfter(index: e5Index)
       
       XCTAssertTrue(success)
       XCTAssertEqual(testGame.moves.count, 2) // Only e4 and e5 remain
       
       // Verify positions were properly updated
       XCTAssertNil(testGame.positions[indices[3]]) // Position after Nf3 deleted
       XCTAssertNotNil(testGame.positions[indices[2]]) // Position after e5 remains
   }
   
   func testDeleteAllAfterWithVariationsIntegration() {
       // Test deleteAllAfter with complex variation structure
       var testGame = Game()
       let mainFinalIndex = testGame.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: MoveTree.minimumIndex)
       let mainIndices = testGame.moves.history(for: mainFinalIndex)
       let e5Index = mainIndices[2]
       
       // Add variations: 2. Nc3 Nf6 and 2. d3 d6
       let _ = testGame.make(moves: ["Nc3", "Nf6"], from: e5Index)
       let _ = testGame.make(moves: ["d3", "d6"], from: e5Index)
       
       // Delete all after e5 (should remove main line and all variations)
       let success = testGame.deleteAllAfter(index: e5Index)
       
       XCTAssertTrue(success)
       XCTAssertEqual(testGame.moves.count, 2) // Only e4 and e5 remain
       
       // Verify no next move or variations exist after e5
       XCTAssertNil(testGame.moves.nextIndex(currentIndex: e5Index))
       XCTAssertEqual(testGame.moves.variations(from: e5Index).count, 0)
       
       // Verify positions were properly cleaned up
       XCTAssertNil(testGame.positions[mainIndices[3]]) // Position after Nf3 deleted
       XCTAssertNil(testGame.positions[mainIndices[4]]) // Position after Nc6 deleted
   }
   
   func testDeleteAllAfterEmptyResult() {
       // Test deleteAllAfter when there's nothing to delete
       var testGame = Game()
       let finalIndex = testGame.make(moves: ["e4", "e5"], from: MoveTree.minimumIndex)
       let indices = testGame.moves.history(for: finalIndex)
       let e5Index = indices[2]
       
       // Try to delete all after e5 (which has no next moves)
       let success = testGame.deleteAllAfter(index: e5Index)
       
       XCTAssertFalse(success) // Should return false as there's nothing to delete
       XCTAssertEqual(testGame.moves.count, 2) // All moves should remain
   }
   
   // MARK: - Overwrite Tests
   
   func testOverwriteWithMove() {
       // Test Game.overwrite(move:from:) basic functionality
       var testGame = Game()
       let finalIndex = testGame.make(moves: ["e4", "e5", "Nf3", "Nc6", "Bc4"], from: MoveTree.minimumIndex)
       let indices = testGame.moves.history(for: finalIndex)
       let e5Index = indices[2]
       
       // Create new move to overwrite from e5 position
       let position = testGame.positions[e5Index]!
       let metaMove = MetaMove(san: "d3", position: position)!
       let newMove = Move(metaMove: metaMove)
       
       // Overwrite from e5 with d3 (should delete Nf3, Nc6, Bc4 and add d3)
       let newIndex = testGame.overwrite(move: newMove, from: e5Index)
       
       XCTAssertNotEqual(newIndex, e5Index) // Should return new index
       XCTAssertEqual(testGame.moves.count, 3) // e4, e5, d3
       
       // Verify new move was added correctly
       let addedMove = testGame.moves.getNodeMove(index: newIndex)
       XCTAssertEqual(addedMove?.metaMove?.san, "d3")
       
       // Verify old moves were deleted
       XCTAssertNil(testGame.positions[indices[3]]) // Position after Nf3 deleted
       XCTAssertNil(testGame.positions[indices[4]]) // Position after Nc6 deleted
       XCTAssertNil(testGame.positions[indices[5]]) // Position after Bc4 deleted
       
       // Verify e5 position still exists
       XCTAssertNotNil(testGame.positions[e5Index])
   }
   
   func testOverwriteWithStringMove() {
       // Test Game.overwrite(move:from:) with string parameter
       var testGame = Game()
       let finalIndex = testGame.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: MoveTree.minimumIndex)
       let indices = testGame.moves.history(for: finalIndex)
       let e5Index = indices[2]
       
       // Overwrite from e5 with string move "Bc4"
       let newIndex = testGame.overwrite(move: "Bc4", from: e5Index)
       
       XCTAssertNotEqual(newIndex, e5Index) // Should return new index
       XCTAssertEqual(testGame.moves.count, 3) // e4, e5, Bc4
       
       // Verify new move was added correctly
       let addedMove = testGame.moves.getNodeMove(index: newIndex)
       XCTAssertEqual(addedMove?.metaMove?.san, "Bc4")
       
       // Verify old moves were deleted
       XCTAssertNil(testGame.positions[indices[3]]) // Position after Nf3 deleted
       XCTAssertNil(testGame.positions[indices[4]]) // Position after Nc6 deleted
   }
   
   func testOverwriteWithVariations() {
       // Test overwrite functionality with complex variation structure
       var testGame = Game()
       let mainFinalIndex = testGame.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: MoveTree.minimumIndex)
       let mainIndices = testGame.moves.history(for: mainFinalIndex)
       let e5Index = mainIndices[2]
       
       // Add variations: 2. Nc3 Nf6 and 2. d3 d6
       let _ = testGame.make(moves: ["Nc3", "Nf6"], from: e5Index)
       let _ = testGame.make(moves: ["d3", "d6"], from: e5Index)
       let initialCount = testGame.moves.count
       
       // Overwrite from e5 with new move (should delete main line and all variations)
       let newIndex = testGame.overwrite(move: "Bb5", from: e5Index)
       
       XCTAssertNotEqual(newIndex, e5Index)
       XCTAssertEqual(testGame.moves.count, 3) // e4, e5, Bb5 (variations deleted)
       XCTAssertTrue(testGame.moves.count < initialCount) // Confirm moves were deleted
       
       // Verify new move was added correctly
       let addedMove = testGame.moves.getNodeMove(index: newIndex)
       XCTAssertEqual(addedMove?.metaMove?.san, "Bb5")
       
       // Verify no variations exist after e5
       XCTAssertEqual(testGame.moves.variations(from: e5Index).count, 0)
   }
   
   func testOverwriteWithInvalidMove() {
       // Test overwrite with invalid string move
       var testGame = Game()
       let finalIndex = testGame.make(moves: ["e4", "e5"], from: MoveTree.minimumIndex)
       let indices = testGame.moves.history(for: finalIndex)
       let e5Index = indices[2]
       
       // Try to overwrite with invalid move
       let resultIndex = testGame.overwrite(move: "InvalidMove", from: e5Index)
       
       XCTAssertEqual(resultIndex, e5Index) // Should return original index on failure
       XCTAssertEqual(testGame.moves.count, 2) // No changes should occur
   }
   
   func testOverwriteWithNoMovesToDelete() {
       // Test overwrite when there are no moves to delete after the target
       var testGame = Game()
       let finalIndex = testGame.make(moves: ["e4", "e5"], from: MoveTree.minimumIndex)
       let indices = testGame.moves.history(for: finalIndex)
       let e5Index = indices[2]
       
       // Overwrite from e5 (no moves after it)
       let newIndex = testGame.overwrite(move: "Nf3", from: e5Index)
       
       XCTAssertEqual(newIndex, e5Index)
       XCTAssertEqual(testGame.moves.count, 2)
       
       // Verify new move was added correctly
       let addedMove = testGame.moves.getNodeMove(index: newIndex)
       XCTAssertEqual(addedMove?.metaMove?.san, "e5")
   }
   
   func testOverwriteAtInvalidIndex() {
       // Test overwrite with invalid index
       var testGame = Game()
       let _ = testGame.make(moves: ["e4", "e5"], from: MoveTree.minimumIndex)
       
       // Try to overwrite at non-existent index
       let resultIndex = testGame.overwrite(move: "Nf3", from: 999)
       
       XCTAssertEqual(resultIndex, 999) // Should return original invalid index
       XCTAssertEqual(testGame.moves.count, 2) // No changes should occur
   }

   func testMakeWithPromotion() {
       // Test Game.makeWithPromotion method
       let fen = "k7/7P/8/8/8/8/8/K7 w - - 0 1"
       var game = Game(startingWith: Position(fen: fen, forceValid: false)!)

       // Create a move that would normally require promotion
       let metaMove = MetaMove(result: .move, piece: Piece(.pawn, color: .white, square: .h7), start: .h7, end: .h8)
       let move = Move(metaMove: metaMove)

       // Test makeWithPromotion with queen promotion
       let newIndex = game.makeWithPromotion(move: move, from: game.startingIndex, promotionPiece: .queen)

       // Verify the position has the promoted queen
       let newPosition = game.positions[newIndex]!
       let promotedPiece = newPosition.piece(at: .h8)
       XCTAssertEqual(promotedPiece?.kind, .queen)
       XCTAssertEqual(promotedPiece?.color, .white)
   }

   func testMakeWithPromotionString() {
       // Test Game.makeWithPromotion with string move
       let fen = "k7/7P/8/8/8/8/8/K7 w - - 0 1"
       var game = Game(startingWith: Position(fen: fen, forceValid: false)!)

       // Test makeWithPromotion with SAN string and queen promotion
       let newIndex = game.makeWithPromotion(move: "h8=Q", from: game.startingIndex, promotionPiece: .queen)

       // Verify the position has the promoted queen
       let newPosition = game.positions[newIndex]!
       let promotedPiece = newPosition.piece(at: .h8)
       XCTAssertEqual(promotedPiece?.kind, .queen)
       XCTAssertEqual(promotedPiece?.color, .white)
   }

   func testPositionMoveWithPromotion() {
       // Test Position.moveWithPromotion method
       let fen = "k7/7P/8/8/8/8/8/K7 w - - 0 1"
       var position = Position(fen: fen, forceValid: false)!

       // Test moveWithPromotion with queen promotion
       let movedPiece = position.moveWithPromotion(pieceAt: .h7, to: .h8, promotionPiece: .queen)

       // Verify the piece was moved and promoted
       XCTAssertEqual(movedPiece?.kind, .queen)
       XCTAssertEqual(movedPiece?.color, .white)
       XCTAssertEqual(movedPiece?.square, .h8)

       // Verify the position has the promoted queen
       let promotedPiece = position.piece(at: .h8)
       XCTAssertEqual(promotedPiece?.kind, .queen)
       XCTAssertEqual(promotedPiece?.color, .white)
   }
}

extension GameTests {

   private static let mockTags = Game.Tags(
       event: "Test Event",
       site: "Barrow, Alaska USA",
       date: "2000.01.01",
       round: "5",
       white: "Player One",
       black: "Player Two",
       result: "1-0",
       annotator: "Annotator",
       plyCount: "15",
       timeControl: "40/7200:3600",
       time: "12:00",
       termination: "abandoned",
       mode: "OTB",
       fen: Position.standard.fen,
       setUp: "1",
       other: [
           "TestKey1": "Test Value 1",
           "TestKey2": "Test Value 2"
       ]
   )

}
