//
//  ChessMove.swift
//  ChessKit
//

/// Represents a move on a chess board.
public struct MetaMove: <PERSON><PERSON><PERSON>, Sendable {

  /// The result of the move.
  public enum Result: <PERSON><PERSON><PERSON>, Sendable {
    case move
    case capture(Piece)
    case castle(Castling)
  }

  /// The check state resulting from the move.
  public enum CheckState: String, Sendable {
    case none
    case check
    case checkmate
    case stalemate

    var notation: String {
      switch self {
      case .none, .stalemate: ""
      case .check: "+"
      case .checkmate: "#"
      }
    }
  }

  /// Rank, file, or square disambiguation of moves.
  public enum Disambiguation: Hashable, Sendable {
    case byFile(Square.File)
    case byRank(Square.Rank)
    case bySquare(Square)
  }

  /// The result of the move.
  public internal(set) var result: Result
  /// The piece that made the move.
  public internal(set) var piece: Piece
  /// The starting square of the move.
  public internal(set) var start: Square
  /// The ending square of the move.
  public internal(set) var end: Square
  /// The piece that was promoted to, if applicable.
  public internal(set) var promotedPiece: Piece?
  /// The move disambiguation, if applicable.
  public internal(set) var disambiguation: Disambiguation?
  /// The check state resulting from the move.
  public internal(set) var checkState: CheckState
  /// The move assessment annotation.
  public var moveAssessment: Assessment
  /// The position assessment annotation.
  public var positionAssessment: Assessment

  /// Initialize a move with the given characteristics.
  public init(
    result: Result,
    piece: Piece,
    start: Square,
    end: Square,
    checkState: CheckState = .none,
    moveAssessment: Assessment = .null,
    positionAssessment: Assessment = .null
  ) {
    self.result = result
    self.piece = piece
    self.start = start
    self.end = end
    self.checkState = checkState
    self.moveAssessment = moveAssessment
    self.positionAssessment = positionAssessment
  }

  /// Initialize a move with a given SAN string.
  ///
  /// This initializer fails if the provided SAN string is invalid.
  public init?(san: String, position: Position) {
    guard let metaMove = SANParser.parse(metaMove: san, in: position) else {
      return nil
    }

    self = metaMove
  }
  
  /// The display description including SAN notation, assessments, and comment
  public var displayDescription: String {
    "\(san)\(moveAssessment.notation)\(positionAssessment.notation)"
  }
  
  /// The PGN description including SAN notation, assessments, and comment
  public var pgnDescription: String {
    let moveAssessmentStr = moveAssessment == .null ? "" : moveAssessment.rawValue
    let positionAssessmentStr = positionAssessment == .null ? "" : positionAssessment.rawValue
    if moveAssessmentStr.isEmpty && positionAssessmentStr.isEmpty {
      return san
    }
    if moveAssessmentStr.isEmpty {
      return san + " " + positionAssessmentStr
    }
    if positionAssessmentStr.isEmpty {
      return san + " " + moveAssessmentStr
    }
    return san + " " + moveAssessmentStr + " " + positionAssessmentStr
  }

  /// The SAN represenation of the move.
  public var san: String {
    SANParser.convert(move: self)
  }

  /// The engine LAN represenation of the move.
  ///
  /// - note: This is intended for engine communication
  /// so piece names, capture/check indicators, etc. are not included.
  public var lan: String {
    EngineLANParser.convert(metaMove: self)
  }

}

// MARK: - Assessment
extension MetaMove {
  public enum Assessment: String, Sendable {
    // 0-9: move quality
    case null = "$0" // null annotation
    case good = "$1" // good move (!)
    case mistake = "$2" // poor move (?)
    case brilliant = "$3" // very good move (!!)
    case blunder = "$4" // very poor move (??)
    case interesting = "$5" // speculative move (!?)
    case dubious = "$6" // questionable move (?!?)
    case forced = "$7" // forced move
    case singular = "$8" // singular move
    case worst = "$9" // worst move
    // 10-135: positional/other annotations
    case drawish = "$10"
    case equalQuiet = "$11"
    case equalActive = "$12"
    case unclear = "$13"
    case whiteSlightAdv = "$14"
    case blackSlightAdv = "$15"
    case whiteModerateAdv = "$16"
    case blackModerateAdv = "$17"
    case whiteDecisiveAdv = "$18"
    case blackDecisiveAdv = "$19"
    case whiteCrushingAdv = "$20"
    case blackCrushingAdv = "$21"
    case whiteZugzwang = "$22"
    case blackZugzwang = "$23"
    case whiteSlightSpace = "$24"
    case blackSlightSpace = "$25"
    case whiteModerateSpace = "$26"
    case blackModerateSpace = "$27"
    case whiteDecisiveSpace = "$28"
    case blackDecisiveSpace = "$29"
    case whiteSlightTime = "$30"
    case blackSlightTime = "$31"
    case whiteModerateTime = "$32"
    case blackModerateTime = "$33"
    case whiteDecisiveTime = "$34"
    case blackDecisiveTime = "$35"
    case whiteInitiative = "$36"
    case blackInitiative = "$37"
    case whiteLastingInitiative = "$38"
    case blackLastingInitiative = "$39"
    case whiteAttack = "$40"
    case blackAttack = "$41"
    case whiteInsufficientComp = "$42"
    case blackInsufficientComp = "$43"
    case whiteSufficientComp = "$44"
    case blackSufficientComp = "$45"
    case whiteMoreThanAdequateComp = "$46"
    case blackMoreThanAdequateComp = "$47"
    case whiteSlightCenter = "$48"
    case blackSlightCenter = "$49"
    case whiteModerateCenter = "$50"
    case blackModerateCenter = "$51"
    case whiteDecisiveCenter = "$52"
    case blackDecisiveCenter = "$53"
    case whiteSlightKingside = "$54"
    case blackSlightKingside = "$55"
    case whiteModerateKingside = "$56"
    case blackModerateKingside = "$57"
    case whiteDecisiveKingside = "$58"
    case blackDecisiveKingside = "$59"
    case whiteSlightQueenside = "$60"
    case blackSlightQueenside = "$61"
    case whiteModerateQueenside = "$62"
    case blackModerateQueenside = "$63"
    case whiteDecisiveQueenside = "$64"
    case blackDecisiveQueenside = "$65"
    case whiteVulnerableFirstRank = "$66"
    case blackVulnerableFirstRank = "$67"
    case whiteWellProtectedFirstRank = "$68"
    case blackWellProtectedFirstRank = "$69"
    case whitePoorlyProtectedKing = "$70"
    case blackPoorlyProtectedKing = "$71"
    case whiteWellProtectedKing = "$72"
    case blackWellProtectedKing = "$73"
    case whitePoorlyPlacedKing = "$74"
    case blackPoorlyPlacedKing = "$75"
    case whiteWellPlacedKing = "$76"
    case blackWellPlacedKing = "$77"
    case whiteVeryWeakPawns = "$78"
    case blackVeryWeakPawns = "$79"
    case whiteModeratelyWeakPawns = "$80"
    case blackModeratelyWeakPawns = "$81"
    case whiteModeratelyStrongPawns = "$82"
    case blackModeratelyStrongPawns = "$83"
    case whiteVeryStrongPawns = "$84"
    case blackVeryStrongPawns = "$85"
    case whitePoorKnightPlacement = "$86"
    case blackPoorKnightPlacement = "$87"
    case whiteGoodKnightPlacement = "$88"
    case blackGoodKnightPlacement = "$89"
    case whitePoorBishopPlacement = "$90"
    case blackPoorBishopPlacement = "$91"
    case whiteGoodBishopPlacement = "$92"
    case blackGoodBishopPlacement = "$93"
    case whitePoorRookPlacement = "$94"
    case blackPoorRookPlacement = "$95"
    case whiteGoodRookPlacement = "$96"
    case blackGoodRookPlacement = "$97"
    case whitePoorQueenPlacement = "$98"
    case blackPoorQueenPlacement = "$99"
    case whiteGoodQueenPlacement = "$100"
    case blackGoodQueenPlacement = "$101"
    case whitePoorCoordination = "$102"
    case blackPoorCoordination = "$103"
    case whiteGoodCoordination = "$104"
    case blackGoodCoordination = "$105"
    case whiteOpeningVeryPoor = "$106"
    case blackOpeningVeryPoor = "$107"
    case whiteOpeningPoor = "$108"
    case blackOpeningPoor = "$109"
    case whiteOpeningWell = "$110"
    case blackOpeningWell = "$111"
    case whiteOpeningVeryWell = "$112"
    case blackOpeningVeryWell = "$113"
    case whiteMiddlegameVeryPoor = "$114"
    case blackMiddlegameVeryPoor = "$115"
    case whiteMiddlegamePoor = "$116"
    case blackMiddlegamePoor = "$117"
    case whiteMiddlegameWell = "$118"
    case blackMiddlegameWell = "$119"
    case whiteMiddlegameVeryWell = "$120"
    case blackMiddlegameVeryWell = "$121"
    case whiteEndingVeryPoor = "$122"
    case blackEndingVeryPoor = "$123"
    case whiteEndingPoor = "$124"
    case blackEndingPoor = "$125"
    case whiteEndingWell = "$126"
    case blackEndingWell = "$127"
    case whiteEndingVeryWell = "$128"
    case blackEndingVeryWell = "$129"
    case whiteSlightCounterplay = "$130"
    case blackSlightCounterplay = "$131"
    case whiteModerateCounterplay = "$132"
    case blackModerateCounterplay = "$133"
    case whiteDecisiveCounterplay = "$134"
    case blackDecisiveCounterplay = "$135"
    // 136-139: time pressure
    case whiteModerateTimePressure = "$136"
    case blackModerateTimePressure = "$137"
    case whiteSevereTimePressure = "$138"
    case blackSevereTimePressure = "$139"
    
    // Non-standard NAGs
    case Novelty = "$146"
    
    /// The human-readable move assessment notation.
    public var notation: String {
      switch self {
      case .null: ""
      case .good: "!"
      case .mistake: "?"
      case .brilliant: "!!"
      case .blunder: "??"
      case .interesting: "!?"
      case .dubious: "?!"
      case .forced: "□"
      case .singular: ""
      case .worst: ""
      case .drawish: "="
      case .equalQuiet: ""
      case .equalActive: ""
      case .unclear: "∞"
      case .whiteSlightAdv: "⩲"
      case .blackSlightAdv: "⩱"
      case .whiteModerateAdv: "±"
      case .blackModerateAdv: "∓"
      case .whiteDecisiveAdv: "+-"
      case .blackDecisiveAdv: "-+"
      case .whiteCrushingAdv: "+--"
      case .blackCrushingAdv: "--+"
      case .whiteZugzwang: "⨀"
      case .blackZugzwang: "⨀"
      case .whiteSlightSpace: ""
      case .blackSlightSpace: ""
      case .whiteModerateSpace: "○"
      case .blackModerateSpace: "○"
      case .whiteDecisiveSpace: ""
      case .blackDecisiveSpace: ""
      case .whiteSlightTime: ""
      case .blackSlightTime: ""
      case .whiteModerateTime: "⟳"
      case .blackModerateTime: "⟳"
      case .whiteDecisiveTime: ""
      case .blackDecisiveTime: ""
      case .whiteInitiative: "↑"
      case .blackInitiative: "↑"
      case .whiteLastingInitiative: ""
      case .blackLastingInitiative: ""
      case .whiteAttack: "→"
      case .blackAttack: "→"
      case .whiteInsufficientComp: ""
      case .blackInsufficientComp: ""
      case .whiteSufficientComp: "=∞"
      case .blackSufficientComp: "=∞"
      case .whiteMoreThanAdequateComp: ""
      case .blackMoreThanAdequateComp: ""
      case .whiteSlightCenter: ""
      case .blackSlightCenter: ""
      case .whiteModerateCenter: ""
      case .blackModerateCenter: ""
      case .whiteDecisiveCenter: ""
      case .blackDecisiveCenter: ""
      case .whiteSlightKingside: ""
      case .blackSlightKingside: ""
      case .whiteModerateKingside: ""
      case .blackModerateKingside: ""
      case .whiteDecisiveKingside: ""
      case .blackDecisiveKingside: ""
      case .whiteSlightQueenside: ""
      case .blackSlightQueenside: ""
      case .whiteModerateQueenside: ""
      case .blackModerateQueenside: ""
      case .whiteDecisiveQueenside: ""
      case .blackDecisiveQueenside: ""
      case .whiteVulnerableFirstRank: ""
      case .blackVulnerableFirstRank: ""
      case .whiteWellProtectedFirstRank: ""
      case .blackWellProtectedFirstRank: ""
      case .whitePoorlyProtectedKing: ""
      case .blackPoorlyProtectedKing: ""
      case .whiteWellProtectedKing: ""
      case .blackWellProtectedKing: ""
      case .whitePoorlyPlacedKing: ""
      case .blackPoorlyPlacedKing: ""
      case .whiteWellPlacedKing: ""
      case .blackWellPlacedKing: ""
      case .whiteVeryWeakPawns: ""
      case .blackVeryWeakPawns: ""
      case .whiteModeratelyWeakPawns: ""
      case .blackModeratelyWeakPawns: ""
      case .whiteModeratelyStrongPawns: ""
      case .blackModeratelyStrongPawns: ""
      case .whiteVeryStrongPawns: ""
      case .blackVeryStrongPawns: ""
      case .whitePoorKnightPlacement: ""
      case .blackPoorKnightPlacement: ""
      case .whiteGoodKnightPlacement: ""
      case .blackGoodKnightPlacement: ""
      case .whitePoorBishopPlacement: ""
      case .blackPoorBishopPlacement: ""
      case .whiteGoodBishopPlacement: ""
      case .blackGoodBishopPlacement: ""
      case .whitePoorRookPlacement: ""
      case .blackPoorRookPlacement: ""
      case .whiteGoodRookPlacement: ""
      case .blackGoodRookPlacement: ""
      case .whitePoorQueenPlacement: ""
      case .blackPoorQueenPlacement: ""
      case .whiteGoodQueenPlacement: ""
      case .blackGoodQueenPlacement: ""
      case .whitePoorCoordination: ""
      case .blackPoorCoordination: ""
      case .whiteGoodCoordination: ""
      case .blackGoodCoordination: ""
      case .whiteOpeningVeryPoor: ""
      case .blackOpeningVeryPoor: ""
      case .whiteOpeningPoor: ""
      case .blackOpeningPoor: ""
      case .whiteOpeningWell: ""
      case .blackOpeningWell: ""
      case .whiteOpeningVeryWell: ""
      case .blackOpeningVeryWell: ""
      case .whiteMiddlegameVeryPoor: ""
      case .blackMiddlegameVeryPoor: ""
      case .whiteMiddlegamePoor: ""
      case .blackMiddlegamePoor: ""
      case .whiteMiddlegameWell: ""
      case .blackMiddlegameWell: ""
      case .whiteMiddlegameVeryWell: ""
      case .blackMiddlegameVeryWell: ""
      case .whiteEndingVeryPoor: ""
      case .blackEndingVeryPoor: ""
      case .whiteEndingPoor: ""
      case .blackEndingPoor: ""
      case .whiteEndingWell: ""
      case .blackEndingWell: ""
      case .whiteEndingVeryWell: ""
      case .blackEndingVeryWell: ""
      case .whiteSlightCounterplay: ""
      case .blackSlightCounterplay: ""
      case .whiteModerateCounterplay: "⇆"
      case .blackModerateCounterplay: "⇆"
      case .whiteDecisiveCounterplay: ""
      case .blackDecisiveCounterplay: ""
      case .whiteModerateTimePressure: ""
      case .blackModerateTimePressure: ""
      case .whiteSevereTimePressure: "⨁"
      case .blackSevereTimePressure: "⨁"
      case .Novelty: "N"
      }
    }
    
    /// Provides human-readable descriptions for assessments without notation
    public var assessmentDescription: String {
        switch self {
        case .null: return "None"
        case .good: return "Good (!)"
        case .mistake: return "Mistake (?)"
        case .brilliant: return "Brilliant (!!)"
        case .blunder: return "Blunder (??)"
        case .interesting: return "Interesting (!?)"
        case .dubious: return "Dubious (?!)"
        case .forced: return "Forced (□)"
        case .whiteZugzwang: return "Zugzwang ⨀"
        case .whiteDecisiveAdv: return "White Decisive (+-)"
        case .whiteModerateAdv: return "White Advantage (±)"
        case .whiteSlightAdv: return "White Slight (⩲)"
        case .drawish: return "Equal (=)"
        case .unclear: return "Unclear (∞)"
        case .blackSlightAdv: return "Black Slight (⩱)"
        case .blackModerateAdv: return "Black Advantage (∓)"
        case .blackDecisiveAdv: return "Black Decisive (-+)"
        case .whiteSufficientComp: return "With Compensation (=∞)"
        case .whiteAttack: return "With Attack (→)"
        case .whiteInitiative: return "Initiative (↑)"
        case .whiteModerateCounterplay: return "Counterplay (⇆)"
        case .Novelty: return "Novelty (N)"
        case .whiteModerateTime: return "Time Advantage (⟳)"
        case .whiteSevereTimePressure: return "Time Pressure (⨁)"
        default: return self.rawValue
        }
    }
    
    public init?(notation: String) {
      switch notation {
      case "": self = .null
      case "!": self = .good
      case "?": self = .mistake
      case "!!": self = .brilliant
      case "??": self = .blunder
      case "!?": self = .interesting
      case "?!": self = .dubious
      case "□": self = .forced
      case "=": self = .drawish
      case "∞": self = .unclear
      case "⩲": self = .whiteSlightAdv
      case "⩱": self = .blackSlightAdv
      case "±": self = .whiteModerateAdv
      case "∓": self = .blackModerateAdv
      case "+-": self = .whiteDecisiveAdv
      case "-+": self = .blackDecisiveAdv
      case "+--": self = .whiteCrushingAdv
      case "--+": self = .blackCrushingAdv
      case "=∞": self = .whiteSufficientComp // blackSufficientComp
      case "⨀": self = .whiteZugzwang // blackZugzwang
      case "○": self = .whiteModerateSpace // blackModerateSpace
      case "⟳": self = .whiteModerateTime // blackModerateTime
      case "↑": self = .whiteInitiative // blackInitiative
      case "→": self = .whiteAttack // blackAttack
      case "⇆": self = .whiteModerateCounterplay // blackModerateCounterplay
      case "⨁": self = .whiteSevereTimePressure // blackSevereTimePressure
      case "N": self = .Novelty
      default: return nil
      }
    }
    
    public enum AssessmentType: Sendable {
      case none
      case moveAssessment
      case positionAssessment
    }
    
    public var assessmentType: AssessmentType {
      if self == .null {
        return .none
      }
      if moveAssessments.contains(self) {
        return .moveAssessment
      }
      return .positionAssessment
    }
  }

  public static let moveAssessments: [Assessment] = [
    .null,
    .good,
    .mistake,
    .interesting,
    .dubious,
    .brilliant,
    .blunder,
    .forced,
    .whiteZugzwang, // blackZugzwang is the same
  ]

  public static let positionAssessments: [Assessment] = [
    .null,
    .whiteDecisiveAdv,
    .whiteModerateAdv,
    .whiteSlightAdv,
    .drawish,
    .unclear,
    .blackSlightAdv,
    .blackModerateAdv,
    .blackDecisiveAdv,
    // for more
    .whiteSufficientComp,
    .whiteAttack,
    .whiteInitiative,
    .whiteModerateCounterplay,
    .Novelty,
    .whiteModerateTime,
    .whiteSevereTimePressure
  ]
}

public struct Move: Hashable, Sendable {
  public var metaMove: MetaMove?
  /// The structured comment associated with a move.
  public var positionComment: PositionComment
  
  public var pgnDiscription: String {
    let metaMoveStr = metaMove?.pgnDescription ?? ""
    let positionStr = positionComment.description
    if metaMoveStr.isEmpty && positionStr.isEmpty {
      return ""
    }
    if metaMoveStr.isEmpty {
      return positionStr
    }
    if positionStr.isEmpty {
      return metaMoveStr
    }
    return metaMoveStr + " " + positionStr
  }
  
  public init(
    metaMove: MetaMove? = nil,
    positionComment: PositionComment = PositionComment()
  ) {
    self.metaMove = metaMove
    self.positionComment = positionComment
  }
}

// MARK: - Comment System
extension Move {
  
  /// Represents different types of comments that can be associated with a move
  public struct PositionComment: Hashable, Sendable, CustomStringConvertible {
    /// The plain text comment
    public var text: String
    
    /// Time annotations for the move
    public var timeAnnotations: TimeAnnotations
    
    /// Visual annotations for the move
    public var visualAnnotations: VisualAnnotations
    
    public init(
      text: String = "",
      timeAnnotations: TimeAnnotations = TimeAnnotations(),
      visualAnnotations: VisualAnnotations = VisualAnnotations()
    ) {
      self.text = text
      self.timeAnnotations = timeAnnotations
      self.visualAnnotations = visualAnnotations
    }
    
    /// Check if the comment is empty (no content)
    public var isEmpty: Bool {
      text.isEmpty && timeAnnotations.isEmpty && visualAnnotations.isEmpty
    }
    
    public var description: String {
      var components: [String] = []
      
      if !text.isEmpty {
        components.append("{ \(text) }")
      }
      
      if !timeAnnotations.isEmpty {
        components.append("{ \(timeAnnotations.description) }")
      }
      
      if !visualAnnotations.isEmpty {
        components.append("{ \(visualAnnotations.description) }")
      }
      
      return components.joined(separator: " ")
    }
    
    /// Parse a PositionComment from a string
    public static func parse(from string: String) -> PositionComment {
      guard !string.isEmpty else {
        return PositionComment()
      }
      
      var text = ""
      var timeAnnotations = TimeAnnotations()
      var visualAnnotations = VisualAnnotations()
      
      // Parse different types of annotations from the string
      let components = parseCommentComponents(from: string)
      
      for component in components {
        if component.hasPrefix("[%clk ") && component.hasSuffix("]") {
          // Time remaining annotation
          let timeStr = String(component.dropFirst(6).dropLast(1)) // Remove "[%clk " and "]"
          timeAnnotations.remainingTime = timeStr
        } else if component.hasPrefix("[%emt ") && component.hasSuffix("]") {
          // Time spent annotation
          let timeStr = String(component.dropFirst(6).dropLast(1)) // Remove "[%emt " and "]"
          timeAnnotations.timeSpent = timeStr
        } else if component.hasPrefix("[%csl ") && component.hasSuffix("]") {
          // Square highlight annotation
          let highlightStr = String(component.dropFirst(6).dropLast(1)) // Remove "[%csl " and "]"
          let highlights = parseSquareHighlights(from: highlightStr)
          visualAnnotations.squareHighlights.append(contentsOf: highlights)
        } else if component.hasPrefix("[%cal ") && component.hasSuffix("]") {
          // Arrow annotation
          let arrowStr = String(component.dropFirst(6).dropLast(1)) // Remove "[%cal " and "]"
          let arrows = parseArrows(from: arrowStr)
          visualAnnotations.arrows.append(contentsOf: arrows)
        } else if !component.hasPrefix("[%") {
          // Plain text comment
          if !text.isEmpty {
            text += " " + component
          } else {
            text = component
          }
        }
      }
      
      return PositionComment(
        text: text,
        timeAnnotations: timeAnnotations,
        visualAnnotations: visualAnnotations
      )
    }
    
    /// Parse comment components from a string, handling nested brackets
    private static func parseCommentComponents(from string: String) -> [String] {
      var components: [String] = []
      var current = ""
      var bracketLevel = 0
      var inBrackets = false
      
      for char in string {
        if char == "[" {
          if bracketLevel == 0 && !current.trimmingCharacters(in: .whitespaces).isEmpty {
            // Add any text before brackets as a component
            components.append(current.trimmingCharacters(in: .whitespaces))
            current = ""
          }
          bracketLevel += 1
          inBrackets = true
          current.append(char)
        } else if char == "]" {
          current.append(char)
          bracketLevel -= 1
          if bracketLevel == 0 {
            // Complete bracket expression
            components.append(current.trimmingCharacters(in: .whitespaces))
            current = ""
            inBrackets = false
          }
        } else if char == " " && !inBrackets {
          // Space outside brackets - potential component separator
          if !current.trimmingCharacters(in: .whitespaces).isEmpty {
            components.append(current.trimmingCharacters(in: .whitespaces))
            current = ""
          }
        } else {
          current.append(char)
        }
      }
      
      // Add remaining text
      if !current.trimmingCharacters(in: .whitespaces).isEmpty {
        components.append(current.trimmingCharacters(in: .whitespaces))
      }
      
      return components
    }
    
    /// Parse square highlights from string like "Gc8,Ra1"
    private static func parseSquareHighlights(from string: String) -> [VisualAnnotations.SquareHighlight] {
      return string.split(separator: ",").compactMap { VisualAnnotations.SquareHighlight(from: String($0).trimmingCharacters(in: .whitespaces)) }
    }
    
    /// Parse arrows from string like "Rh5g3,Ge3e4"
    private static func parseArrows(from string: String) -> [VisualAnnotations.Arrow] {
      return string.split(separator: ",").compactMap { VisualAnnotations.Arrow(from: String($0).trimmingCharacters(in: .whitespaces)) }
    }
  }
  
  /// Time-related annotations
  public struct TimeAnnotations: Hashable, Sendable, CustomStringConvertible {
    
    /// Remaining time on the clock (format: HH:MM:SS)
    public var remainingTime: String?
    
    /// Time spent on this move (format: HH:MM:SS)
    public var timeSpent: String?
    
    public init(remainingTime: String? = nil, timeSpent: String? = nil) {
      self.remainingTime = remainingTime
      self.timeSpent = timeSpent
    }
    
    public var isEmpty: Bool {
      remainingTime == nil && timeSpent == nil
    }
    
    public var description: String {
      var components: [String] = []
      
      if let clk = remainingTime {
        components.append("[%clk \(clk)]")
      }
      
      if let emt = timeSpent {
        components.append("[%emt \(emt)]")
      }
      
      return components.joined(separator: " ")
    }
  }
  
  /// Visual annotations for board display
  public struct VisualAnnotations: Hashable, Sendable, CustomStringConvertible {
    
    /// Square highlights
    public var squareHighlights: [SquareHighlight]
    
    /// Arrows on the board
    public var arrows: [Arrow]
    
    public init(squareHighlights: [SquareHighlight] = [], arrows: [Arrow] = []) {
      self.squareHighlights = squareHighlights
      self.arrows = arrows
    }
    
    public var isEmpty: Bool {
      squareHighlights.isEmpty && arrows.isEmpty
    }
    
    public var description: String {
      var components: [String] = []
      
      if !squareHighlights.isEmpty {
        let highlights = squareHighlights.map { $0.description }.joined(separator: ",")
        components.append("[%csl \(highlights)]")
      }
      
      if !arrows.isEmpty {
        let arrowDescs = arrows.map { $0.description }.joined(separator: ",")
        components.append("[%cal \(arrowDescs)]")
      }
      
      return components.joined(separator: " ")
    }
      
  /// Color for visual annotations
  public enum AnnotationColor: String, CaseIterable, Hashable, Sendable {
    case red = "R"
    case green = "G"
    case blue = "B"
    
    public var displayName: String {
      switch self {
      case .red: return "Red"
      case .green: return "Green"
      case .blue: return "Blue"
      }
    }
  }
  
  /// Square highlight annotation
  public struct SquareHighlight: Hashable, Sendable, Equatable {
    public var color: AnnotationColor
    public var square: Square
    
    public init(color: AnnotationColor, square: Square) {
      self.color = color
      self.square = square
    }
    
    public var description: String {
      "\(color.rawValue)\(square.notation)"
    }
    
    /// Parse from string format like "Gc8"
    public init?(from string: String) {
      guard string.count >= 3 else {
        return nil
      }
      
      let colorChar = string.first!.description
      guard let color = AnnotationColor(rawValue: colorChar) else {
        return nil
      }
      let square = Square(String(string.dropFirst()))
      
      self.color = color
      self.square = square
    }
  }
  
  /// Arrow annotation
  public struct Arrow: Hashable, Sendable, Equatable {
    public var color: AnnotationColor
    public var from: Square
    public var to: Square
    
    public init(color: AnnotationColor, from: Square, to: Square) {
      self.color = color
      self.from = from
      self.to = to
    }
    
    public var description: String {
      "\(color.rawValue)\(from.notation)\(to.notation)"
    }
    
    /// Parse from string format like "Rh5g3"
    public init?(from string: String) {
      guard string.count >= 5,
            let colorChar = string.first,
            let color = AnnotationColor(rawValue: String(colorChar)) else {
        return nil
      }
      
      let squares = String(string.dropFirst())
      guard squares.count == 4 else {
        return nil
      }
      
      let from = Square(String(squares.prefix(2)))
      let to = Square(String(squares.suffix(2)))
      
      self.color = color
      self.from = from
      self.to = to
    }
  }
  }
}
