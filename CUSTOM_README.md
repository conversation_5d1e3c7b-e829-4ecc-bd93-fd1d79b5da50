# ChessKit 1.4.0 API Guide

This document provides comprehensive usage examples for the ChessKit 1.4.0 API. This version introduces multi-game PGN parsing capabilities, intelligent result determination, optimized UndoState structures with memory improvements, encapsulated index resolution methods, enhanced parser robustness for edge cases, and enhanced API design for better performance and maintainability.

## Table of Contents

- [Core Concepts](#core-concepts)
- [Undo/Redo System (v1.2.2)](#undoredo-system-v122)
- [FEN Validation (v1.1.0)](#fen-validation-v110)
- [Basic Game Creation](#basic-game-creation)
- [Working with Moves](#working-with-moves)
- [Move Tree Operations](#move-tree-operations)
- [Comments and Annotations](#comments-and-annotations)
- [PGN Parsing and Generation](#pgn-parsing-and-generation)
  - [Multi-Game PGN Parsing](#multi-game-pgn-parsing-v130)
  - [Intelligent Result Determination](#intelligent-result-determination-v130)
- [Advanced Move Tree Editing](#advanced-move-tree-editing)
- [Migration from 1.1.x](#migration-from-11x)

## Core Concepts

### Move Structure Redesign

ChessKit 1.0.0 completely redesigns the move structure:

```swift
// NEW: Move now contains two components
struct Move {
    var metaMove: MetaMove?           // The actual chess move (optional for head nodes)
    var positionComment: PositionComment  // Rich comment system
}

// MetaMove contains the core move information
struct MetaMove {
    let result: Result              // move, capture, castle
    let piece: Piece               // piece that moved
    let start: Square              // starting square
    let end: Square                // ending square
    let promotedPiece: Piece?      // promoted piece if applicable
    let disambiguation: Disambiguation?
    let checkState: CheckState     // none, check, checkmate, stalemate
    var moveAssessment: Assessment // !, ?, !!, etc.
    var positionAssessment: Assessment // +/-, =, ∞, etc.
}

// Rich comment system
struct PositionComment {
    var text: String                        // Plain text comment
    var timeAnnotations: TimeAnnotations    // [%clk], [%emt]
    var visualAnnotations: VisualAnnotations // [%csl], [%cal]
}
```

### MoveTree Integer Indexing

The move tree now uses simple integer indexing instead of complex `Index` structures:

```swift
typealias MoveIndex = Int

// Head node always has index -1
static let minimumIndex: MoveIndex = -1

// Move indices are now hash values (v1.2.0+)
```

## Undo/Redo System (v1.2.2)

ChessKit 1.2.2 features an optimized undo/redo system with enhanced memory efficiency and encapsulated API design that supports all chess operations:

### Basic Undo/Redo Operations

```swift
import ChessKit

var game = Game()

// Make moves with undo capability
let (index1, undoState1) = game.makeWithUndo(move: "e4")
let (index2, undoState2) = game.makeWithUndo(move: "e5", from: index1)

// Undo the last move
let success = game.undoMake(undoState2)
print(success) // true

// Redo the undone move
let redoIndex = game.redoMake(undoState2)
print(redoIndex == index2) // true
```

### Advanced Undo/Redo Operations

```swift
// Delete moves with undo
let (deleteSuccess, deleteUndoState) = game.moves.deleteWithUndo(at: index1)
if deleteSuccess {
    // Restore deleted moves
    let restoreSuccess = game.moves.undoDelete(deleteUndoState!)
}

// Promote variations with undo
let (promoteSuccess, promoteUndoState) = game.moves.promoteWithUndo(index: variationIndex)
if promoteSuccess {
    // Undo promotion
    let undoSuccess = game.moves.undoPromote(promoteUndoState!)
}

// Edit moves with undo
let editUndoState = game.moves.setNodeMoveWithUndo(index: index1, newMove: newMove)
if let undoState = editUndoState {
    // Undo edit
    let undoSuccess = game.moves.undoEditMove(undoState)
}
```

### Undo State Types

ChessKit provides 7 specialized undo state types:

```swift
// 1. AddMoveUndoState - for move addition
// 2. DeleteMoveUndoState - for move deletion
// 3. DeleteBeforeMoveUndoState - for "delete before move" operations
// 4. PromoteVariationUndoState - for variation promotion
// 5. PromoteToMainUndoState - for promoting to main variation
// 6. OverwriteUndoState - for move overwrite operations
// 7. EditMoveUndoState - for move editing
```

## FEN Validation (v1.1.0)

ChessKit 1.1.0 introduces comprehensive FEN (Forsyth-Edwards Notation) validation with detailed chess rule checking.

### Enhanced FEN Parser

The `FENParser` now includes complete validation logic that checks:

#### Basic Structure Validation
- Exactly 6 fields separated by spaces
- Proper field format for each component

#### Piece Placement Validation
- Exactly 8 ranks separated by '/'
- Valid characters only (piece letters and digits 1-8)
- Each rank represents exactly 8 squares
- No consecutive numbers in rank notation

#### Field-Specific Validation
```swift
// Active color validation
let validColors = ["w", "b"]

// Castling rights validation  
let validCastling = ["K", "Q", "k", "q", "-"]
// No duplicates, proper character set

// En passant validation
let validEnPassant = ["-", "a3", "b3", ..., "h3", "a6", "b6", ..., "h6"]

// Move counters validation
// Halfmove clock: >= 0
// Fullmove number: >= 1
```

#### Chess Logic Validation
```swift
// Position validation with FEN string
let fen = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"

if let position = Position(fen: fen) {
    print("Valid position")
} else {
    print("Invalid position - validation failed")
}

// The validation includes:
// 1. King count: exactly one king per side
// 2. Pawn placement: no pawns on ranks 1 or 8
// 3. Piece count: maximum 16 pieces per side, 8 pawns per side
// 4. Castling logic: kings and rooks in correct positions for castling rights
// 5. En passant logic: proper pawn positions and turn order
// 6. Check logic: non-active side's king cannot be in check
```

#### Castling Rights Logic
```swift
// If FEN includes castling rights, the corresponding pieces must be in position
let fenWithCastling = "r3k2r/8/8/8/8/8/8/R3K2R w KQkq - 0 1"

// Validation checks:
// 'K': White king on e1, white rook on h1
// 'Q': White king on e1, white rook on a1  
// 'k': Black king on e8, black rook on h8
// 'q': Black king on e8, black rook on a8

if Position(fen: fenWithCastling) != nil {
    print("Castling rights are consistent with piece positions")
}
```

#### En Passant Logic
```swift
// En passant validation ensures logical consistency
let enPassantFen = "rnbqkbnr/ppp1pppp/8/3pP3/8/8/PPPP1PPP/RNBQKBNR b KQkq d6 0 2"

// Validation checks:
// - If en passant target is on rank 3, active color must be black
// - If en passant target is on rank 6, active color must be white  
// - The captured pawn must be in the correct position
// - The target square must be empty

if Position(fen: enPassantFen) != nil {
    print("En passant target is logically valid")
}
```

#### Check Validation
```swift
// The parser validates that the non-active side's king is not in check
let invalidCheck = "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2"

// This would be invalid if the black king were in check from a white piece
// when it's white's turn to move (indicating an impossible position)
```

### Usage Examples

```swift
// Basic validation
func validateFEN(_ fenString: String) -> Bool {
    return Position(fen: fenString) != nil
}

// Custom position creation with validation
func createCustomPosition() -> Position? {
    let customFEN = "r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4"
    
    // FENParser.parse now includes comprehensive validation
    return Position(fen: customFEN) // Returns nil if validation fails
}

// Error handling for invalid positions
func handlePositionInput(_ fenString: String) {
    if let position = Position(fen: fenString) {
        print("Valid position created successfully")
        // Proceed with position
    } else {
        print("Invalid FEN: Position does not meet chess rules")
        // Handle error - position violates chess logic
    }
}
```

### Migration Notes for v1.1.0

The enhanced validation is backward compatible - existing code will continue to work, but now benefit from more robust FEN validation:

```swift
// OLD: Limited validation (basic format only)
let position = Position(fen: someString) // Might accept invalid chess positions

// NEW: Comprehensive validation (format + chess rules)  
let position = Position(fen: someString) // Only accepts valid chess positions
```

All validation errors result in `nil` being returned from the FEN parser, maintaining the same API surface while providing much more reliable validation.

## Basic Game Creation

### Creating a New Game

```swift
import ChessKit

// Create a standard game
var game = Game()

// Create a game with custom starting position
let position = Position(fen: "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1")!
var gameFromPosition = Game(startingWith: position)

// Get the starting move index (usually -1 for head node)
let startIndex = MoveTree.minimumIndex
```

### Making Moves

```swift
// Method 1: Using SAN notation strings
let moveIndex1 = game.make(move: "e4", from: MoveTree.minimumIndex)
let moveIndex2 = game.make(move: "e5", from: moveIndex1)

// Method 2: Creating MetaMove and Move objects
let position = game.positions[moveIndex2]!
if let metaMove = SANParser.parse(metaMove: "Nf3", in: position) {
    let move = Move(metaMove: metaMove)
    let moveIndex3 = game.make(move: move, from: moveIndex2)
}

// Method 3: Making multiple moves in sequence
let indices = game.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: MoveTree.minimumIndex)
```

## Working with Moves

### Creating MetaMoves

```swift
// From SAN notation
let position = Position.standard
if let metaMove = SANParser.parse(metaMove: "e4", in: position) {
    print(metaMove.san) // "e4"
    print(metaMove.piece) // Piece(color: .white, kind: .pawn)
    print(metaMove.start) // Square("e2")
    print(metaMove.end) // Square("e4")
}

// Manual creation
let metaMove = MetaMove(
    result: .move,
    piece: Piece(color: .white, kind: .pawn),
    start: Square("e2"),
    end: Square("e4"),
    checkState: .none,
    moveAssessment: .good,
    positionAssessment: .whiteSlightAdv
)
```

### Working with Assessments

```swift
// Move assessments (for the quality of the move)
let moveAssessments: [MetaMove.Assessment] = [
    .good,      // !
    .mistake,   // ?
    .brilliant, // !!
    .blunder,   // ??
    .interesting, // !?
    .dubious    // ?!
]

// Position assessments (for the resulting position)
let positionAssessments: [MetaMove.Assessment] = [
    .whiteDecisiveAdv,  // +-
    .whiteModerateAdv,  // ±
    .whiteSlightAdv,    // ⩲
    .drawish,           // =
    .unclear,           // ∞
    .blackSlightAdv,    // ⩱
    .blackModerateAdv,  // ∓
    .blackDecisiveAdv   // -+
]

// Creating a move with assessments
var metaMove = MetaMove(/*...*/)
metaMove.moveAssessment = .brilliant
metaMove.positionAssessment = .whiteDecisiveAdv

// Display descriptions
print(metaMove.moveAssessment.notation)         // "!!"
print(metaMove.positionAssessment.notation)     // "+-"
print(metaMove.displayDescription)              // "e4!!+-"
```

## Move Tree Operations

### Navigation

```swift
// Get the first move index
let firstIndex = game.moves.firstIndex

// Navigate through moves
if let currentIndex = firstIndex {
    let nextIndex = game.moves.nextIndex(currentIndex: currentIndex)
    let previousIndex = game.moves.previousIndex(currentIndex: currentIndex)
}

// Check if on main variation
let isMainLine = game.moves.isOnMainVariation(index: someIndex)

// Get move history and future
let history = game.moves.history(for: someIndex)     // All moves leading to this position
let future = game.moves.future(for: someIndex)       // All moves from this position forward
let fullVariation = game.moves.fullVariation(for: someIndex) // Combined history + future
```

### Finding Moves

```swift
// Check if a specific move exists from a position
let hasMove = game.moves.hasNextMove(containing: someMove, for: currentIndex)

// Get variations from a position
let variations = game.moves.variations(from: currentIndex)
```

### Move Tree Statistics

```swift
let totalMoves = game.moves.allCount        // Total number of moves including variations
let mainLineMoves = game.moves.count        // Number of moves in main line only
let hasVariations = game.moves.hasVariation // Whether tree contains any variations
let isEmpty = game.moves.isEmpty             // Whether tree is empty
```

## Comments and Annotations

### Text Comments

```swift
// Create a move with a text comment
let positionComment = Move.PositionComment(text: "This is an excellent opening move!")
let move = Move(metaMove: metaMove, positionComment: positionComment)

// Add comments to existing positions
var headMove = Move(positionComment: Move.PositionComment(text: "Starting position analysis"))
game.moves.setNodeMove(index: MoveTree.minimumIndex, newMove: headMove)
```

### Time Annotations

```swift
// Create time annotations
let timeAnnotations = Move.TimeAnnotations(
    remainingTime: "01:30:25",  // [%clk 01:30:25]
    timeSpent: "00:02:15"       // [%emt 00:02:15]
)

let positionComment = Move.PositionComment(
    text: "Long calculation here",
    timeAnnotations: timeAnnotations
)
```

### Visual Annotations

```swift
// Square highlights
let squareHighlight = Move.VisualAnnotations.SquareHighlight(
    color: .red,
    square: Square("e4")
)

// Arrows
let arrow = Move.VisualAnnotations.Arrow(
    color: .green,
    from: Square("e2"),
    to: Square("e4")
)

// Combine visual annotations
let visualAnnotations = Move.VisualAnnotations(
    squareHighlights: [squareHighlight],
    arrows: [arrow]
)

let positionComment = Move.PositionComment(
    text: "Key square and attacking idea",
    visualAnnotations: visualAnnotations
)
```

### Parsing Complex Comments

```swift
// Parse comments from PGN-style strings
let complexComment = "Great move! [%clk 01:20:52] [%emt 00:03:15] [%csl Rd4,Ge4] [%cal Gc2d4,Re4f6]"
let parsedComment = Move.PositionComment.parse(from: complexComment)

print(parsedComment.text)                          // "Great move!"
print(parsedComment.timeAnnotations.remainingTime) // "01:20:52"
print(parsedComment.timeAnnotations.timeSpent)     // "00:03:15"
print(parsedComment.visualAnnotations.squareHighlights.count) // 2
print(parsedComment.visualAnnotations.arrows.count) // 2
```

## PGN Parsing and Generation

### Parsing PGN

```swift
let pgnString = """
[Event "World Championship"]
[Site "New York"]
[Date "2025.01.13"]
[Round "1"]
[White "Player 1"]
[Black "Player 2"]
[Result "1-0"]

1. e4! {Excellent opening} e5 2. Nf3 Nc6 3. Bb5 {Spanish Opening} a6 
4. Ba4 Nf6 5. O-O Be7 6. Re1 b5 7. Bb3 d6 1-0
"""

// Parse the PGN
if let game = PGNParser.parse(game: pgnString) {
    print("Parsed game with \(game.moves.count) moves")
    print("Event: \(game.tags.event)")
    print("Result: \(game.tags.result)")
    
    // Access moves and their comments
    if let firstMoveIndex = game.moves.firstIndex,
       let firstMove = game.moves.getNodeMove(index: firstMoveIndex) {
        print("First move: \(firstMove.metaMove?.san ?? "")")
        print("Comment: \(firstMove.positionComment.text)")
    }
}
```

### Generating PGN

```swift
// Convert game back to PGN
let pgnOutput = PGNParser.convert(game: game)
print(pgnOutput)

// Get PGN representation as structured elements
let pgnElements = game.moves.pgnRepresentation
for element in pgnElements {
    switch element {
    case .whiteNumber(let number):
        print("\(number).")
    case .blackNumber(let number):
        print("\(number)...")
    case .move(let move, let index):
        print(move.pgnDiscription)
    case .variationStart:
        print("(")
    case .variationEnd:
        print(")")
    }
}
```

### Multi-Game PGN Parsing (v1.4.0)

ChessKit 1.4.0 introduces enhanced multi-game PGN parsing with improved edge case handling.

```swift
let multiGamePGN = """
[Event "Tournament Round 1"]
[White "Player A"]
[Black "Player B"]
[Result "1-0"]

1. e4 e5 2. Nf3 Nc6 3. Bb5 1-0

[Event "Tournament Round 2"] 
[White "Player C"]
[Black "Player D"]
[Result "0-1"]

1. d4 d5 2. c4 c6 3. Nf3 Nf6 0-1

[Event "Tournament Round 3"]
[White "Player E"]
[Black "Player F"]
[Result "1/2-1/2"]

1. c4 e5 2. Nc3 Nf6 3. g3 d5 1/2-1/2
"""

// Parse multiple games
let games = PGNParser.parseMultiple(games: multiGamePGN)
print("Parsed \(games.count) games")

for (index, game) in games.enumerated() {
    print("Game \(index + 1): \(game.tags.event) - \(game.tags.result)")
    print("Moves: \(game.positions.count - 1)")
}
```

#### Enhanced Edge Case Support (v1.4.0)
```swift
// Games without PGN tags (now properly supported)
let simpleMultiGame = """
1. e4 e5 *
1. d4 Nf6 *
"""

let games = PGNParser.parseMultiple(games: simpleMultiGame)
print("Parsed \(games.count) games") // Correctly parses 2 games

// Mixed format games
let mixedPGN = """
[Event "Tagged Game"]
[Result "1-0"]
1. e4 e5 1-0

1. d4 d5 *

[Event "Another Tagged Game"] 
[Result "0-1"]
1. c4 c5 0-1
"""

let mixedGames = PGNParser.parseMultiple(games: mixedPGN)
print("Parsed \(mixedGames.count) games") // Handles all 3 games correctly
```

#### Game Boundary Recognition
The parser recognizes three types of game boundaries:

1. **Tag-separated**: `* [Event "..."]` - Result followed by PGN tag
2. **File-end**: `*` - Result at end of input  
3. **Move-separated**: `* 1. e4` - Result followed by move number (v1.4.0 enhancement)

#### Features
- **Automatic Game Boundary Detection**: Enhanced regex patterns for robust game separation
- **Format Handling**: Supports various line ending formats (Windows, Mac, Unix)
- **Error Resilience**: Invalid games are filtered out, valid games are preserved
- **Edge Case Support**: Handles games without tags, mixed formats, and unusual boundaries
- **Performance Optimized**: Efficient regex-based parsing for large PGN collections

### Intelligent Result Determination (v1.3.0)

The PGN converter now intelligently determines game results when the Result tag is empty:

```swift
let gameWithoutResult = """
[Event "Test Game"]
[White "Player A"]
[Black "Player B"]

1. f3 e5 2. g4 Qh4#
"""

if let game = PGNParser.parse(game: gameWithoutResult) {
    let pgnOutput = PGNParser.convert(game: game)
    print(pgnOutput)
    // Output will end with "0-1" (Black wins by checkmate)
}
```

#### Intelligent Result Logic
- **Existing Result Preserved**: If `[Result "..."]` tag exists and is not empty, it takes priority
- **Checkmate Detection**: Analyzes the `CheckState` of the last move
  - `checkmate` → Returns appropriate result (`1-0` or `0-1`) based on the side that delivered checkmate
  - `stalemate` → Returns `1/2-1/2` (draw)
  - `check` or `none` → Returns `*` (game ongoing)
- **Fallback Handling**: If game state cannot be determined, defaults to `*`

#### Example Use Cases
```swift
// Checkmate scenarios
let foolsMate = "1. f3 e5 2. g4 Qh4#"  // → "0-1"
let scholarsMate = "1. e4 e5 2. Bc4 Nc6 3. Qh5 Nf6 4. Qxf7#"  // → "1-0"

// Stalemate scenario (when properly detected by move analysis)
let stalemateGame = "..."  // → "1/2-1/2"

// Ongoing game
let incompleteGame = "1. e4 e5 2. Nf3 Nc6"  // → "*"
```

## Advanced Move Tree Editing

### Adding Variations

```swift
// Add a variation to an existing move
let mainLineIndex = game.make(move: "e4", from: MoveTree.minimumIndex)
let blackResponse = game.make(move: "e5", from: mainLineIndex)

// Add alternative for Black (French Defense)
let frenchDefense = game.make(move: "e6", from: mainLineIndex)

// Add alternative for White (different opening)
let d4Opening = game.make(move: "d4", from: MoveTree.minimumIndex)
```

### Deleting Moves

```swift
// Delete a move and all subsequent moves
let success = game.moves.delete(at: someIndex)
if success {
    print("Move and all descendants deleted")
}

// Note: Cannot delete the head node (minimumIndex)
```

### Promoting Variations

```swift
// Promote a variation to higher priority
let promoted = game.moves.promote(index: variationIndex)

// Promote a variation to become the main line
let promotedToMain = game.moves.promoteToMainVariation(index: variationIndex)

if promotedToMain {
    print("Variation is now the main line")
}
```

### Overwriting Moves

The overwrite API allows you to replace a move and all subsequent variations with a new move, providing a clean way to modify games from any position.

```swift
// Create a game with some moves
var game = Game()
let e4Index = game.make(move: "e4", from: MoveTree.minimumIndex)
let e5Index = game.make(move: "e5", from: e4Index)
let nf3Index = game.make(move: "Nf3", from: e5Index)
let nc6Index = game.make(move: "Nc6", from: nf3Index)

// Add a variation at the e5 position
let frenchIndex = game.make(move: "e6", from: e4Index)

print("Before overwrite: \(game.moves.count) moves") // 5 moves (e4, e5, Nf3, Nc6, e6)

// Overwrite from e5 with a different move (Bb5)
// This will delete Nf3, Nc6, and the e6 variation
let newIndex = game.overwrite(move: "Bb5", from: e5Index)

print("After overwrite: \(game.moves.count) moves")  // 3 moves (e4, e5, Bb5)
print("New move: \(game.moves.getNodeMove(index: newIndex)?.metaMove?.san ?? "")") // "Bb5"
```

```swift
// You can also overwrite using a Move object
let position = game.positions[e5Index]!
let metaMove = MetaMove(san: "Bc4", position: position)!
let move = Move(metaMove: metaMove)

let anotherIndex = game.overwrite(move: move, from: e5Index)
```

```swift
// Overwrite handles edge cases gracefully
// If there are no moves to delete, it simply adds the new move
var simpleGame = Game()
let e4Only = simpleGame.make(move: "e4", from: MoveTree.minimumIndex)

// This will just add e5 since there are no moves after e4
let e5Added = simpleGame.overwrite(move: "e5", from: e4Only)
print("Moves after overwrite: \(simpleGame.moves.count)") // 2 moves

// Invalid moves return the original index
let invalidResult = simpleGame.overwrite(move: "InvalidMove", from: e4Only)
print("Invalid move result: \(invalidResult == e4Only)") // true
```

**Key Features:**
- **Clean Replacement:** Removes all moves and variations after the target position
- **Error Handling:** Returns original index on failure (invalid moves, invalid indices)
- **Efficient:** Leverages existing deletion and addition operations
- **Flexible:** Works with both string moves and Move objects

### Finding Variation Roots

```swift
// Find the root of the variation containing a move
if let rootIndex = game.moves.getVariationRootNodeIndex(index: someIndex) {
    print("Variation starts at move index: \(rootIndex)")
}

// Check if a move is on the main variation
if game.moves.isOnMainVariation(index: someIndex) {
    print("This move is on the main line")
} else {
    print("This move is in a variation")
}
```

### Path Finding

```swift
// Find the path between two positions
let path = game.moves.path(from: startIndex, to: endIndex)
for (direction, index) in path {
    switch direction {
    case .forward:
        print("Make move at index \(index)")
    case .reverse:
        print("Undo move at index \(index)")
    }
}
```

## Migration from 1.1.x

### Hash-Based Indexing Changes

If your code relies on specific index values or sequential indexing:

```swift
// OLD (1.1.x): Assuming sequential indices
let firstMoveIndex = 0
let secondMoveIndex = 1

// NEW (1.2.0): Use actual returned indices
let firstMoveIndex = game.make(move: "e4")
let secondMoveIndex = game.make(move: "e5", from: firstMoveIndex)
```

### Undo/Redo Integration

Update your move operations to use undo-capable methods:

```swift
// OLD (1.1.x): Basic operations
let moveIndex = game.make(move: "e4")

// NEW (1.2.0): Undo-capable operations
let (moveIndex, undoState) = game.makeWithUndo(move: "e4")
// Store undoState for potential undo operations
```

### Bug Fixes

Update your variation selection methods to use proper session management:

```swift
// OLD (1.1.x): Direct index manipulation
self.currentMoveIndex = option.index

// NEW (1.2.0): Use session for state management
self.session?.goToMove(at: option.index)
```

### Test Code Updates

For test files expecting specific index values:

```swift
// OLD (1.1.x): Hardcoded index expectations
XCTAssertEqual(moveIndex, 0)

// NEW (1.2.0): Use helper methods
XCTAssertEqual(moveIndex, game.moves.nthIndex(0))
```

---

## Migration from 0.x

### Key Changes

1. **Move Structure**: Replace `Move` properties with `MetaMove` + `PositionComment`
2. **Indexing**: Replace `MoveTree.Index` with simple `Int`
3. **Assessments**: Split single assessment into move and position assessments
4. **Comments**: Use structured `PositionComment` instead of simple strings

### Migration Examples

```swift
// OLD (0.x)
let move = Move(san: "e4", position: position)!
move.assessment = .good
move.comment = "Excellent move!"
let index = MoveTree.Index(color: .white, number: 1, variation: 0)

// NEW (1.0.0)
let metaMove = SANParser.parse(metaMove: "e4", in: position)!
metaMove.moveAssessment = .good
let positionComment = Move.PositionComment(text: "Excellent move!")
let move = Move(metaMove: metaMove, positionComment: positionComment)
let index: MoveTree.MoveIndex = 0 // Simple integer

// OLD (0.x)
let nextIndex = tree.nextIndex(for: currentIndex)

// NEW (1.0.0)
let nextIndex = tree.nextIndex(currentIndex: currentIndex)

// OLD (0.x)
tree.annotate(moveAt: index, assessment: .good, comment: "Great!")

// NEW (1.0.0)
if var move = tree.getNodeMove(index: index) {
    move.metaMove?.moveAssessment = .good
    move.positionComment.text = "Great!"
    tree.setNodeMove(index: index, newMove: move)
}
```

This guide covers the major API changes and usage patterns in ChessKit 1.0.0. The new architecture provides much more flexibility for chess analysis applications, advanced editing features, and rich annotation support.